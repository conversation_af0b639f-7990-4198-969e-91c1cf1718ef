---
description: 
globs: 
alwaysApply: false
---
# API & Database Patterns - Kbuy.me

## 🔗 Endpoint Structure

### Public API (Landing Page)
```typescript
// POST /api/track
// Validasi kode tracking dan redirect
interface TrackCodeRequest {
  code: string;
  userAgent?: string;
  ipAddress?: string;
}

// GET /api/track/:code  
// Direct redirect via URL parameter
// Example: kbuy.me/s/eko123
```

### KOL API
```typescript
// GET /api/kol/campaigns
// Daftar campaign yang tersedia untuk KOL

// POST /api/kol/join-campaign
// KOL join campaign dan dapat kode unik
interface JoinCampaignRequest {
  campaignId: string;
  kolId: string;
}

// GET /api/kol/stats/:kolId
// Statistik performa KOL
interface KolStats {
  totalClicks: number;
  totalCommissions: number;
  activeCampaigns: number;
  clicksToday: number;
}
```

### Vendor API  
```typescript
// POST /api/vendor/campaigns
// Buat campaign baru
interface CreateCampaignRequest {
  title: string;
  description: string;
  targetUrl: string;
  commissionPerClick: number;
  startDate: Date;
  endDate: Date;
  maxKolCount?: number;
}

// GET /api/vendor/campaigns/:vendorId
// Daftar campaign milik vendor

// GET /api/vendor/analytics/:campaignId
// Analytics campaign per KOL
```

## 🗄️ Database Relations

### Core Models
```typescript
// User model (Vendor + KOL)
interface User {
  id: string;
  email: string;
  name: string;
  role: 'vendor' | 'kol';
  phone?: string;
  bankAccount?: string;
  isActive: boolean;
}

// Campaign model
interface Campaign {
  id: string;
  vendorId: string;
  title: string;
  description: string;
  targetUrl: string;
  commissionPerClick: number;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  maxKolCount?: number;
  currentKolCount: number;
}

// TrackingCode model
interface TrackingCode {
  id: string;
  code: string; // unique identifier like "eko123"
  kolId: string;
  campaignId: string;
  isActive: boolean;
  generatedAt: Date;
}

// ClickLog model  
interface ClickLog {
  id: string;
  trackingCodeId: string;
  ipAddress: string;
  userAgent: string;
  referer?: string;
  timestamp: Date;
  isConverted: boolean;
}
```

### Commission & Withdrawal
```typescript
// Commission model
interface Commission {
  id: string;
  kolId: string;
  campaignId: string;
  clickLogId: string;
  amount: number;
  status: 'pending' | 'approved' | 'paid';
  createdAt: Date;
  paidAt?: Date;
}

// WithdrawRequest model
interface WithdrawRequest {
  id: string;
  kolId: string;
  amount: number;
  bankAccount: string;
  status: 'pending' | 'processing' | 'completed' | 'rejected';
  requestedAt: Date;
  processedAt?: Date;
  notes?: string;
}
```

## 🔄 Business Logic Patterns

### Tracking Flow
```typescript
const processTrackingClick = async (code: string, metadata: ClickMetadata) => {
  // 1. Validasi kode tracking
  const trackingCode = await validateTrackingCode(code);
  if (!trackingCode.success) {
    throw new Error('Kode tidak valid');
  }

  // 2. Log klik ke database
  const clickLog = await logClick({
    trackingCodeId: trackingCode.data.id,
    ...metadata
  });

  // 3. Hitung dan simpan komisi
  await calculateCommission(clickLog.id);

  // 4. Return redirect URL
  return trackingCode.data.campaign.targetUrl;
}
```

### Commission Calculation
```typescript
const calculateCommission = async (clickLogId: string) => {
  const clickLog = await getClickLogWithRelations(clickLogId);
  const campaign = clickLog.trackingCode.campaign;
  
  // Hitung komisi berdasarkan rate campaign
  const grossCommission = campaign.commissionPerClick;
  const platformFee = grossCommission * 0.1; // 10% platform fee
  const netCommission = grossCommission - platformFee;

  await createCommission({
    kolId: clickLog.trackingCode.kolId,
    campaignId: campaign.id,
    clickLogId: clickLogId,
    amount: netCommission,
    status: 'pending'
  });
}
```

### Code Generation
```typescript
const generateUniqueKolCode = async (kolId: string, campaignId: string): Promise<string> => {
  const kol = await getUserById(kolId);
  const campaign = await getCampaignById(campaignId);
  
  // Generate code dengan format: {kolName}{campaignId_suffix}
  const baseCode = `${kol.name.toLowerCase().replace(/\s+/g, '')}${campaign.id.slice(-3)}`;
  
  // Pastikan unique dengan menambah suffix jika perlu
  let finalCode = baseCode;
  let counter = 1;
  
  while (await codeExists(finalCode)) {
    finalCode = `${baseCode}${counter}`;
    counter++;
  }
  
  return finalCode;
}
```

## 📊 Analytics Queries

### KOL Performance
```sql
-- Query untuk statistik KOL
SELECT 
  u.name as kol_name,
  COUNT(cl.id) as total_clicks,
  SUM(com.amount) as total_earnings,
  COUNT(DISTINCT tc.campaign_id) as active_campaigns
FROM users u
JOIN tracking_codes tc ON u.id = tc.kol_id  
JOIN click_logs cl ON tc.id = cl.tracking_code_id
JOIN commissions com ON cl.id = com.click_log_id
WHERE u.role = 'kol' 
  AND cl.timestamp >= NOW() - INTERVAL '30 days'
GROUP BY u.id, u.name;
```

### Campaign Performance
```sql
-- Query untuk performa campaign
SELECT 
  c.title,
  COUNT(cl.id) as total_clicks,
  COUNT(DISTINCT tc.kol_id) as participating_kols,
  SUM(com.amount) as total_payouts
FROM campaigns c
JOIN tracking_codes tc ON c.id = tc.campaign_id
JOIN click_logs cl ON tc.id = cl.tracking_code_id  
JOIN commissions com ON cl.id = com.click_log_id
WHERE c.is_active = true
GROUP BY c.id, c.title
ORDER BY total_clicks DESC;
```

## 🔐 Security & Validation

### Input Sanitization
```typescript
import { z } from 'zod';

const TrackingCodeSchema = z.object({
  code: z.string()
    .min(3, 'Kode minimal 3 karakter')
    .max(20, 'Kode maksimal 20 karakter')  
    .regex(/^[a-zA-Z0-9]+$/, 'Kode hanya boleh huruf dan angka')
});

const CampaignSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().max(1000),
  targetUrl: z.string().url('URL tidak valid'),
  commissionPerClick: z.number().positive('Komisi harus positif'),
  startDate: z.date(),
  endDate: z.date()
});
```

### Rate Limiting
```typescript
// Implementasi rate limiting untuk tracking endpoint
const trackingRateLimit = rateLimit({
  windowMs: 1000, // 1 detik
  max: 5, // maksimal 5 request per detik per IP
  message: 'Terlalu banyak request, coba lagi nanti',
  standardHeaders: true,
  legacyHeaders: false,
});
```
