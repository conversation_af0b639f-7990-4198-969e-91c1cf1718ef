---
description: 
globs: 
alwaysApply: false
---
# Konfigurasi Teknologi - Kbuy.me

## 🛠️ Stack Teknologi Utama

### Frontend
- **Framework**: React 18+ atau Next.js 14+
- **Language**: TypeScript untuk type safety
- **Styling**: Tailwind CSS atau styled-components
- **State Management**: Zustand atau Redux Toolkit
- **UI Components**: Shadcn/ui atau Chakra UI

### Backend  
- **Runtime**: Node.js 20+ atau Bun
- **Framework**: Express.js atau Fastify
- **Database**: PostgreSQL atau MySQL
- **ORM**: Prisma atau TypeORM
- **Authentication**: JWT atau NextAuth.js

### DevOps & Tools
- **Package Manager**: pnpm atau npm
- **Bundler**: Vite atau Webpack
- **Testing**: Vitest atau Jest
- **Linting**: ESLint + Prettier
- **Type Checking**: TypeScript strict mode

## 📝 Standar Koding TypeScript

### Naming Conventions
```typescript
// Interface & Type - PascalCase dengan prefix
interface IUser {
  id: string;
  name: string;
}

type TKolStats = {
  clicks: number;
  commissions: number;
}

// Function - camelCase dengan deskripsi jelas  
const generateKolCode = (kolId: string, campaignId: string): string => {
  // Implementasi generate kode tracking untuk KOL
}

// Component - PascalCase
const KolDashboard = ({ kolId }: { kolId: string }) => {
  // Komponen dashboard untuk KOL
}
```

### Error Handling
```typescript
// Gunakan Result pattern untuk error handling
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E }

// Contoh implementasi
const validateTrackingCode = async (code: string): Promise<Result<Campaign>> => {
  try {
    const campaign = await findCampaignByCode(code);
    if (!campaign) {
      return { success: false, error: new Error('Kode tidak ditemukan') };
    }
    return { success: true, data: campaign };
  } catch (error) {
    return { success: false, error: error as Error };
  }
}
```

### API Response Format
```typescript
// Standar response API
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

// Contoh untuk tracking klik
interface ClickTrackingResponse {
  success: boolean;
  message: string;
  redirectUrl: string;
  trackingId: string;
}
```

## 🔒 Security Best Practices

### Input Validation
- Validasi semua input dengan Zod atau Joi
- Sanitize data sebelum menyimpan ke database
- Rate limiting untuk API endpoints

### Authentication & Authorization
- Implementasi JWT dengan refresh token
- Role-based access control (RBAC)
- Session management yang aman

## 📊 Database Patterns

### Prisma Schema Example
```prisma
model TrackingCode {
  id         String   @id @default(cuid())
  code       String   @unique
  kolId      String
  campaignId String
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  kol      User      @relation("KolCodes", fields: [kolId], references: [id])
  campaign Campaign  @relation(fields: [campaignId], references: [id])
  clicks   ClickLog[]

  @@map("tracking_codes")
}
```

## 🧪 Testing Standards
- Unit test untuk business logic
- Integration test untuk API endpoints  
- E2E test untuk critical user flows
- Mock external API calls

## 📦 File Organization
```
src/
├── components/     # Reusable UI components
├── pages/         # Next.js pages atau route handlers
├── hooks/         # Custom React hooks
├── utils/         # Helper functions
├── types/         # TypeScript type definitions
├── services/      # API service layer
├── stores/        # State management
└── constants/     # App constants
```

## 🚀 Performance Optimization
- Lazy loading untuk components
- Image optimization dengan Next.js Image
- Database query optimization
- Caching strategy untuk frequently accessed data
