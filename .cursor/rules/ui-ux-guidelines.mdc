---
description: 
globs: 
alwaysApply: false
---
# UI/UX Guidelines - Kbuy.me

## 🎨 Design System

### Brand Identity
- **Primary Color**: #3B82F6 (Blue) - untuk tracking dan aksi utama
- **Secondary Color**: #10B981 (Green) - untuk komisi dan earnings
- **Accent Color**: #F59E0B (Orange) - untuk notifikasi dan highlight
- **Neutral**: #6B7280 (Gray) - untuk teks dan border
- **Error**: #EF4444 (Red) - untuk error states

### Typography
```css
/* Heading Font */
font-family: 'Inter', 'system-ui', sans-serif;

/* Body Font */  
font-family: 'Inter', 'system-ui', sans-serif;

/* Monospace (untuk kode tracking) */
font-family: 'JetBrains Mono', 'Consolas', monospace;
```

### Spacing Scale
- xs: 4px
- sm: 8px  
- md: 16px
- lg: 24px
- xl: 32px
- 2xl: 48px

## 📱 Responsive Design

### Breakpoints
```css
/* Mobile First Approach */
sm: 640px   /* Mobile landscape */
md: 768px   /* Tablet portrait */
lg: 1024px  /* Tablet landscape / Desktop */
xl: 1280px  /* Large desktop */
2xl: 1536px /* Extra large desktop */
```

### Component Priorities
1. **Mobile**: Optimasi untuk input kode tracking
2. **Tablet**: Dashboard KOL yang mudah digunakan
3. **Desktop**: Analytics dan reporting yang komprehensif

## 🏠 Landing Page (kbuy.me)

### Hero Section
```typescript
// Komponen utama landing page
const LandingPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header dengan logo Kbuy.me */}
      <header className="text-center py-12">
        <h1 className="text-4xl font-bold text-gray-900">
          Kbuy.me
        </h1>
        <p className="text-lg text-gray-600 mt-2">
          Platform Tracking & Komisi KOL
        </p>
      </header>

      {/* Input kode tracking - komponen utama */}
      <main className="max-w-md mx-auto px-4">
        <TrackingCodeInput />
      </main>

      {/* Footer dengan info platform */}
      <footer className="text-center py-8 text-gray-500">
        <p>Untuk KOL & Vendor: Daftar sekarang!</p>
      </footer>
    </div>
  );
};
```

### Input Component
```typescript
const TrackingCodeInput = () => {
  const [code, setCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  return (
    <div className="bg-white rounded-2xl shadow-xl p-8">
      <h2 className="text-2xl font-semibold text-center mb-6">
        Masukkan Kode Promo
      </h2>
      
      <div className="space-y-4">
        <input
          type="text"
          value={code}
          onChange={(e) => setCode(e.target.value.toUpperCase())}
          placeholder="Contoh: EKO123"
          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg
                   focus:border-blue-500 focus:ring-2 focus:ring-blue-200
                   text-center text-lg font-mono tracking-wider"
          maxLength={20}
        />
        
        <button
          onClick={handleSubmit}
          disabled={!code || isLoading}
          className="w-full py-3 bg-blue-600 text-white font-semibold rounded-lg
                   hover:bg-blue-700 disabled:bg-gray-300 
                   transition-colors duration-200"
        >
          {isLoading ? 'Memproses...' : 'Lihat Promo'}
        </button>
      </div>

      {/* Info tambahan */}
      <p className="text-sm text-gray-500 text-center mt-4">
        Masukkan kode dari KOL favorit Anda
      </p>
    </div>
  );
};
```

## 👨‍💼 Dashboard KOL

### Navigation
```typescript
const KolSidebar = () => {
  const menuItems = [
    { icon: 'chart', label: 'Dashboard', href: '/kol' },
    { icon: 'campaigns', label: 'Campaign', href: '/kol/campaigns' },
    { icon: 'codes', label: 'Kode Saya', href: '/kol/codes' },
    { icon: 'earnings', label: 'Earnings', href: '/kol/earnings' },
    { icon: 'withdraw', label: 'Withdraw', href: '/kol/withdraw' },
  ];

  return (
    <nav className="w-64 bg-white shadow-lg h-screen">
      {/* Logo & Profile */}
      <div className="p-6 border-b">
        <h2 className="text-xl font-bold">KOL Panel</h2>
        <p className="text-gray-500">Halo, {kolName}!</p>
      </div>

      {/* Menu Items */}
      <ul className="py-4">
        {menuItems.map(item => (
          <li key={item.href}>
            <Link href={item.href} 
                  className="flex items-center px-6 py-3 hover:bg-blue-50">
              <Icon name={item.icon} className="mr-3" />
              {item.label}
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  );
};
```

### Stats Cards
```typescript
const KolStatsCard = ({ title, value, icon, change, color = 'blue' }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-500 text-sm font-medium">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-1">{value}</p>
          {change && (
            <p className={`text-sm mt-1 ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change > 0 ? '+' : ''}{change}% dari bulan lalu
            </p>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg bg-${color}-100 flex items-center justify-center`}>
          <Icon name={icon} className={`w-6 h-6 text-${color}-600`} />
        </div>
      </div>
    </div>
  );
};
```

## 🏭 Dashboard Vendor

### Campaign Management
```typescript
const CampaignCard = ({ campaign }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            {campaign.title}
          </h3>
          <p className="text-gray-500 mt-1">{campaign.description}</p>
        </div>
        <StatusBadge status={campaign.status} />
      </div>

      {/* Campaign Stats */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <p className="text-2xl font-bold text-blue-600">{campaign.kolCount}</p>
          <p className="text-xs text-gray-500">KOL Aktif</p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-bold text-green-600">{campaign.clicks}</p>
          <p className="text-xs text-gray-500">Total Klik</p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-bold text-orange-600">
            Rp {campaign.totalCommission.toLocaleString()}
          </p>
          <p className="text-xs text-gray-500">Total Komisi</p>
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-2">
        <button className="flex-1 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
          Edit Campaign
        </button>
        <button className="flex-1 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
          Lihat Detail
        </button>
      </div>
    </div>
  );
};
```

## 📊 Data Visualization

### Chart Components
```typescript
// Menggunakan Recharts untuk visualisasi data
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const ClickTrendChart = ({ data }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h3 className="text-lg font-semibold mb-4">Trend Klik 30 Hari</h3>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip 
            labelFormatter={(value) => `Tanggal: ${value}`}
            formatter={(value) => [`${value} klik`, 'Jumlah Klik']}
          />
          <Line 
            type="monotone" 
            dataKey="clicks" 
            stroke="#3B82F6" 
            strokeWidth={2}
            dot={{ fill: '#3B82F6', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};
```

## 🔔 Notifications & Alerts

### Toast Notifications
```typescript
// Menggunakan react-hot-toast
import toast from 'react-hot-toast';

// Success notification
const showSuccess = (message: string) => {
  toast.success(message, {
    duration: 4000,
    position: 'top-right',
    style: {
      background: '#10B981',
      color: 'white',
    },
  });
};

// Error notification  
const showError = (message: string) => {
  toast.error(message, {
    duration: 5000,
    position: 'top-right',
    style: {
      background: '#EF4444',
      color: 'white',
    },
  });
};

// Contoh penggunaan
const handleTrackingCode = async (code: string) => {
  try {
    const result = await trackCode(code);
    showSuccess('Kode valid! Mengarahkan ke promo...');
    window.location.href = result.redirectUrl;
  } catch (error) {
    showError('Kode tidak ditemukan atau sudah tidak aktif');
  }
};
```

## 🎯 UX Best Practices

### Loading States
```typescript
// Skeleton loading untuk dashboard
const StatsSkeleton = () => {
  return (
    <div className="animate-pulse">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-xl p-6 shadow-sm">
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/3"></div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Error States
```typescript
const ErrorState = ({ onRetry, message = 'Terjadi kesalahan' }) => {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <Icon name="exclamation" className="w-8 h-8 text-red-600" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Oops! {message}
      </h3>
      <p className="text-gray-500 mb-6">
        Silakan coba lagi atau hubungi support jika masalah berlanjut.
      </p>
      <button 
        onClick={onRetry}
        className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
      >
        Coba Lagi
      </button>
    </div>
  );
};
```

## 📱 Mobile Optimization

### Touch Targets
- Minimum 44px untuk semua button dan link
- Spacing minimal 8px antar elemen interaktif
- Thumb-friendly navigation pada mobile

### Performance
- Lazy loading untuk images dan heavy components
- Code splitting untuk dashboard modules  
- Progressive Web App (PWA) capabilities
- Offline state handling untuk tracking codes
