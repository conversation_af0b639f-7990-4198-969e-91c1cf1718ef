---
description: 
globs: 
alwaysApply: true
---
# Aturan Umum Pengembangan - Kbuy.me

## 🎯 Bahasa & Komunikasi
- Gunakan **bahasa Indonesia** dalam semua komunikasi dan dokumentasi
- Komentar kode ditulis dalam bahasa Indonesia yang jelas dan mudah dipahami
- Nama variable dan function boleh bahasa Inggris, tapi dokumentasi harus Indonesia

## 📋 Workflow Wajib
### 1. Sebelum Edit Kode:
- **WAJIB** panggil `get-project-info` untuk memahami konteks proyek
- Analisis struktur proyek dan dependensi yang ada
- Pastikan perubahan konsisten dengan arsitektur yang sudah ada

### 2. Setelah Edit Kode:
- **WAJIB** panggil `update-project-info` 
- Ikuti instruksi dari respons `update-project-info` dengan cermat
- Update dokumentasi terkait jika diperlukan

## 📏 Batasan File
- **Maksimal 300 baris per file** - tidak boleh lebih!
- Jika kode melebihi 300 baris, **WAJIB** dipecah menjadi beberapa file modular
- Gunakan konvensi penamaan yang jelas: `feature.moduleA.js`, `feature.moduleB.js`
- Setiap file harus memiliki tanggung jawab yang jelas (single responsibility)

## 🔍 Dokumentasi Terkini
- Untuk informasi terbaru tentang library/framework, gunakan `context7`
- Pastikan menggunakan versi dan best practices terbaru
- Dokumentasi API harus selalu up-to-date

## ✅ Standar Kualitas
- Kode harus dapat dijalankan langsung tanpa error
- Tambahkan semua import dan dependency yang diperlukan
- Implementasi error handling yang proper
- Tulis test jika memungkinkan

## 🎨 UI/UX (untuk Frontend)
- Gunakan design modern dan responsif
- Implementasi best practices UX
- Pastikan accessibility standards terpenuhi

## 📚 Referensi Proyek
- File utama dokumentasi: [project.md](mdc:project.md)
- Rencana implementasi: [rencana-implementasi.md](mdc:rencana-implementasi.md)
