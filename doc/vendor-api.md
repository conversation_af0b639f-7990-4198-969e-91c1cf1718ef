# Dokumentasi API Vendor Kbuy.me

## 1. Vendor Management

### Register Vendor
- **POST** `/api/vendor/register`
- Deskripsi: Registrasi vendor baru (server-to-server)
- Body: 
  ```json
  {
    "name": "Skincare ABC",
    "email": "<EMAIL>",
    "phone_number": "08123456789",
    "source": "klikbuy"
  }
  ```
- Response:
  ```json
  {
    "success": true,
    "message": "Vendor berhasil didaftarkan",
    "data": { "id": 1, "name": "Skincare ABC", ... }
  }
  ```

### Cek ID Vendor
- **GET** `/api/vendor/check-id?email=<EMAIL>`
- Deskripsi: Cek apakah vendor id/email/phone sudah terdaftar
- Query params: `email`, `phone_number`, atau `id`
- Response:
  ```json
  {
    "success": true,
    "message": "Sudah terdaftar",
    "data": { "exists": true }
  }
  ```

### Get Profile Vendor
- **GET** `/api/vendor`
- Deskripsi: Ambil profil vendor (autentikasi)
- Headers: `Authorization: Bearer {token}`

### Update Profile Vendor
- **PUT** `/api/vendor`
- Deskripsi: Update profil vendor
- Headers: `Authorization: Bearer {token}`
- Body: 
  ```json
  {
    "about": "Brand skincare terpercaya",
    "profile_pic_url": "https://example.com/logo.jpg",
    "brand_category": "kecantikan",
    "business_type": "B2C"
  }
  ```

### Hapus Akun Vendor
- **DELETE** `/api/vendor`
- Deskripsi: Hapus akun vendor
- Headers: `Authorization: Bearer {token}`

### Statistik Vendor
- **GET** `/api/public/vendor/{id}`
- Deskripsi: Detail vendor untuk KOL (lihat statistik, profil, promo aktif)

---

## 2. Promo Management

### Create Promo
- **POST** `/api/vendor/promo`
- Deskripsi: Buat promo baru
- Headers: `Authorization: Bearer {token}`
- Body: 
  ```json
  {
    "nama_promo": "Promo Skincare Ramadan",
    "deskripsi": "Diskon 50% untuk semua produk skincare",
    "start_date": "2025-01-15 00:00:00",
    "end_date": "2025-01-31 23:59:59",
    "budget_limit": 5000000,
    "commission_model": "single",
    "target_type": "whatsapp",
    "target_value": "wa.me/628123456789",
    "komisi_per_closing": 25000,
    "komisi_type": "fixed"
  }
  ```

### List Promo
- **GET** `/api/vendor/promo?status=1&start_date=2025-01-01`
- Deskripsi: List promo milik vendor (filter: status, date, budget)
- Headers: `Authorization: Bearer {token}`
- Query params: `status`, `start_date`, `budget_remaining`

### Detail Promo
- **GET** `/api/vendor/promo/{id}`
- Deskripsi: Detail promo (budget, status, dsb)
- Headers: `Authorization: Bearer {token}`

### Update Promo
- **PUT** `/api/vendor/promo/{id}`
- Deskripsi: Update promo
- Headers: `Authorization: Bearer {token}`

### Hapus Promo
- **DELETE** `/api/vendor/promo/{id}`
- Deskripsi: Hapus promo
- Headers: `Authorization: Bearer {token}`

### Pause/Unpause Campaign
- **PUT** `/api/vendor/promo/{id}/pause`
- Deskripsi: Pause/unpause campaign

### Extend Campaign
- **PUT** `/api/vendor/promo/{id}/extend`
- Deskripsi: Perpanjang tanggal/budget campaign

### Budget Tracking
- **GET** `/api/vendor/promo/{id}/budget-tracking`
- Deskripsi: Tracking penggunaan budget campaign

---

## 3. Marketing Kit Management

### Tambah Marketing Kit
- **POST** `/api/vendor/promo/{promo_id}/marketing-kit`
- Deskripsi: Tambah marketing kit ke promo
- Headers: `Authorization: Bearer {token}`
- Body:
  ```json
  {
    "nama_kit": "Banner Promo Ramadan",
    "tipe": "image",
    "url_file": "https://example.com/banner.jpg",
    "file_size": 512000
  }
  ```

### List Marketing Kit
- **GET** `/api/vendor/promo/{promo_id}/marketing-kit`
- Deskripsi: List marketing kit promo
- Headers: `Authorization: Bearer {token}`

### Hapus Marketing Kit
- **DELETE** `/api/vendor/marketing-kit/{id}`
- Deskripsi: Hapus marketing kit
- Headers: `Authorization: Bearer {token}`

---

## 4. Endpoint Public (KOL)

### List Vendor
- **GET** `/api/public/vendors?brand_category=kecantikan&min_rating=4&sort=rating&order=desc`
- Deskripsi: List semua vendor/brand
- Query params: `brand_category`, `min_rating`, `is_verified`, `sort`, `order`
- Response (paginated):
  ```json
  {
    "success": true,
    "message": "Daftar vendor",
    "data": {
      "data": [...],
      "current_page": 1,
      "per_page": 15,
      "total": 50
    }
  }
  ```

### Detail Vendor
- **GET** `/api/public/vendor/{id}`
- Deskripsi: Detail lengkap vendor untuk KOL (include promo aktif)

### List Promo Vendor
- **GET** `/api/public/vendor/{id}/promos`
- Deskripsi: List promo aktif dari vendor

### Search Vendor
- **GET** `/api/public/vendors/search?keyword=skincare&category=kecantikan&min_commission=50000`
- Deskripsi: Search vendor dengan multiple filter
- Query params: `keyword`, `category`, `min_commission`, `max_commission`, `min_rating`, `location`, `business_type`, `is_verified`

### Rekomendasi Vendor
- **GET** `/api/public/vendors/recommended`
- Deskripsi: Rekomendasi vendor (rating ≥4.0, komisi ≥50k)

### Trending Vendor
- **GET** `/api/public/vendors/trending`
- Deskripsi: Vendor trending (berdasarkan total_kol_partners)

### Vendor Baru
- **GET** `/api/public/vendors/new`
- Deskripsi: Vendor baru join platform

---

## 5. Follow & Review

### Follow Vendor
- **POST** `/api/public/vendor/{id}/follow`
- Deskripsi: KOL follow vendor
- Headers: `Authorization: Bearer {token}`

### Unfollow Vendor
- **DELETE** `/api/public/vendor/{id}/follow`
- Deskripsi: KOL unfollow vendor
- Headers: `Authorization: Bearer {token}`

### List Vendor yang di-follow
- **GET** `/api/public/kol/{kol_id}/followed-vendors`
- Deskripsi: List vendor yang di-follow KOL
- Headers: `Authorization: Bearer {token}`

### Review Vendor
- **POST** `/api/public/vendor/{id}/review`
- Deskripsi: KOL kasih review ke vendor
- Headers: `Authorization: Bearer {token}`
- Body:
  ```json
  {
    "rating": 5,
    "review": "Vendor yang sangat recommended, payment cepat dan campaign menarik!",
    "campaign_id": 123
  }
  ```

### List Review Vendor
- **GET** `/api/public/vendor/{id}/reviews?rating=5&is_verified=1`
- Deskripsi: List review dari KOL lain
- Query params: `rating`, `is_verified`

### Update Review
- **PUT** `/api/public/vendor/{id}/review/{review_id}`
- Deskripsi: Update review KOL
- Headers: `Authorization: Bearer {token}`

### Hapus Review
- **DELETE** `/api/public/vendor/{id}/review/{review_id}`
- Deskripsi: Hapus review KOL
- Headers: `Authorization: Bearer {token}`

---

## 6. Notifikasi & Feed

### List Notifikasi
- **GET** `/api/kol/notifications?is_read=0&type=new_campaign`
- Deskripsi: List notifikasi untuk KOL
- Headers: `Authorization: Bearer {token}`
- Query params: `is_read`, `type`, `date_from`, `date_to`

### Mark Notifikasi Read
- **PUT** `/api/kol/notifications/{id}/read`
- Deskripsi: Tandai notifikasi sudah dibaca
- Headers: `Authorization: Bearer {token}`

### Mark All Read
- **PUT** `/api/kol/notifications/mark-all-read`
- Deskripsi: Tandai semua notifikasi sudah dibaca
- Headers: `Authorization: Bearer {token}`

### Hapus Notifikasi
- **DELETE** `/api/kol/notifications/{id}`
- Deskripsi: Hapus notifikasi
- Headers: `Authorization: Bearer {token}`

### Feed Timeline
- **GET** `/api/kol/feed`
- Deskripsi: Feed timeline campaign baru/update

### Mark Feed Seen
- **PUT** `/api/kol/feed/{id}/seen`
- Deskripsi: Tandai feed sudah dilihat

### Unread Count
- **GET** `/api/kol/feed/unread-count`
- Deskripsi: Hitung notifikasi/feed belum dibaca

---

## 7. Performance & Automation

### Live Performance Vendor
- **GET** `/api/public/vendor/{id}/live-performance`
- Deskripsi: Data performa vendor hari ini

### Analytics Promo
- **GET** `/api/public/promo/{id}/analytics`
- Deskripsi: Analytics promo tertentu

### History Performa Vendor
- **GET** `/api/public/vendor/{id}/performance-history`
- Deskripsi: History performa vendor per bulan

### Campaign Leaderboard
- **GET** `/api/public/campaign-leaderboard`
- Deskripsi: Leaderboard campaign/vendor

### Automation & Cron
- **POST** `/api/system/check-campaign-budgets`
- **POST** `/api/system/check-campaign-dates`
- **GET** `/api/system/campaigns/expiring-soon`
- **PUT** `/api/system/campaign/{id}/auto-stop`
- Deskripsi: Endpoint internal untuk automation/monitoring

---

## 8. Response Format Standar

### Success Response
```json
{
  "success": true,
  "message": "Operasi berhasil",
  "data": { ... }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "data": null
}
```

### HTTP Status Codes
- `200` - OK
- `201` - Created
- `400` - Bad Request (validasi error)
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error

---

## 9. Authentication
Semua endpoint yang memerlukan autentikasi menggunakan **Bearer Token**:
```
Authorization: Bearer {your_access_token}
```

## 10. Pagination
Endpoint yang mengembalikan list data menggunakan pagination Laravel:
```json
{
  "data": [...],
  "current_page": 1,
  "per_page": 15,
  "total": 100,
  "last_page": 7
}
```
