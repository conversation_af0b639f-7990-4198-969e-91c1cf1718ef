# Dokumentasi API Vendor - Swagger/OpenAPI

## 📋 Deskripsi

Folder ini berisi dokumentasi API Vendor untuk platform Kbuy.me dalam format OpenAPI/Swagger 3.0. Dokumentasi ini mencakup semua endpoint API yang telah diimplementasikan untuk manajemen vendor, promo, marketing kit, follow system, review system, dan notifikasi.

## 📁 File Dokumentasi

- `vendor-api.yaml` - Dokumentasi lengkap API Vendor dalam format OpenAPI 3.0

## 🌐 Cara Mengakses Dokumentasi

### 1. Swagger UI Online

Kunjungi [Swagger Editor](https://editor.swagger.io/) dan:
1. Copy isi file `vendor-api.yaml`
2. Paste ke Swagger Editor
3. Dokumentasi akan ditampilkan dengan UI yang interaktif

### 2. Local Swagger UI

Jika menggunakan Swagger UI secara lokal:

```bash
# Install swagger-ui-serve
npm install -g swagger-ui-serve

# Jalankan dokumentasi
swagger-ui-serve doc/swagger/vendor-api.yaml
```

### 3. Laravel L5 Swagger (Opsional)

Jika ingin mengintegrasikan dengan Laravel project:

```bash
# Install L5 Swagger
composer require darkaonline/l5-swagger

# Publish config
php artisan vendor:publish --provider "L5Swagger\L5SwaggerServiceProvider"

# Generate documentation
php artisan l5-swagger:generate
```

## 🔧 Fitur Dokumentasi

### Endpoint yang Didokumentasikan

#### 🏢 Vendor Management
- **POST** `/vendor/register` - Registrasi vendor baru
- **GET** `/vendor/check-id` - Cek ketersediaan ID/email/phone
- **GET** `/vendor` - Ambil profil vendor
- **PUT** `/vendor` - Update profil vendor
- **DELETE** `/vendor` - Hapus akun vendor

#### 🎯 Promo Management
- **POST** `/vendor/promo` - Buat promo baru
- **GET** `/vendor/promo` - List promo dengan filter
- **GET** `/vendor/promo/{id}` - Detail promo
- **PUT** `/vendor/promo/{id}` - Update promo
- **DELETE** `/vendor/promo/{id}` - Hapus promo

#### 📄 Marketing Kit
- **POST** `/vendor/promo/{promo_id}/marketing-kit` - Tambah marketing kit
- **GET** `/vendor/promo/{promo_id}/marketing-kit` - List marketing kit
- **DELETE** `/vendor/marketing-kit/{id}` - Hapus marketing kit

#### 🌍 Public Endpoints
- **GET** `/public/vendors` - List vendor untuk KOL
- **GET** `/public/vendor/{id}` - Detail vendor
- **GET** `/public/vendors/search` - Search vendor

#### 👥 Follow & Review System
- **POST** `/public/vendor/{id}/follow` - Follow vendor
- **DELETE** `/public/vendor/{id}/follow` - Unfollow vendor
- **POST** `/public/vendor/{id}/review` - Buat review
- **GET** `/public/vendor/{id}/review` - List review

#### 🔔 Notifikasi
- **GET** `/kol/notifications` - List notifikasi
- **PUT** `/kol/notifications/{id}/read` - Mark as read
- **PUT** `/kol/notifications/mark-all-read` - Mark all as read

### Fitur Dokumentasi

✅ **Request/Response Schema** - Schema lengkap untuk setiap endpoint
✅ **Parameter Validation** - Validasi input dan query parameter
✅ **HTTP Status Codes** - Response codes dengan penjelasan
✅ **Authentication** - Bearer token untuk endpoint yang memerlukan auth
✅ **Examples** - Contoh request dan response JSON
✅ **Categorization** - Endpoint dikelompokkan berdasarkan fungsi
✅ **Error Handling** - Format error response terstandarisasi

## 🔐 Authentication

API menggunakan Bearer Token authentication:

```bash
# Header yang diperlukan
Authorization: Bearer {your-jwt-token}
Content-Type: application/json
```

### Endpoint Tanpa Authentication
- `/vendor/register`
- `/vendor/check-id`
- `/public/*` (semua endpoint public)

### Endpoint Dengan Authentication
- `/vendor` (GET, PUT, DELETE)
- `/vendor/promo/*`
- `/vendor/marketing-kit/*`
- `/kol/notifications/*`

## 📊 Response Format

Semua endpoint menggunakan format response yang konsisten:

### Success Response
```json
{
  "success": true,
  "message": "Operasi berhasil",
  "data": {
    // ... data response
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "data": null
}
```

### Paginated Response
```json
{
  "success": true,
  "message": "Data berhasil diambil",
  "data": {
    "data": [...],
    "current_page": 1,
    "per_page": 15,
    "total": 100,
    "last_page": 7
  }
}
```

## 🧪 Testing API

### Menggunakan Swagger UI
1. Buka dokumentasi di Swagger UI
2. Klik "Authorize" dan masukkan Bearer token
3. Pilih endpoint yang ingin ditest
4. Isi parameter yang diperlukan
5. Klik "Execute"

### Menggunakan cURL
```bash
# Example: Register vendor
curl -X POST "http://localhost:8000/api/vendor/register" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Vendor",
    "email": "<EMAIL>",
    "phone_number": "08123456789",
    "source": "klikbuy"
  }'

# Example: Get vendor list (authenticated)
curl -X GET "http://localhost:8000/api/public/vendors" \
  -H "Authorization: Bearer {your-token}"
```

### Menggunakan Postman
1. Import file `vendor-api.yaml` ke Postman
2. Set environment variable untuk base URL dan token
3. Jalankan collection tests

## 🔄 Update Dokumentasi

Jika ada perubahan API:

1. **Update file YAML**
   ```bash
   # Edit file dokumentasi
   vim doc/swagger/vendor-api.yaml
   ```

2. **Validasi sintaks**
   - Gunakan Swagger Editor untuk validasi
   - Pastikan tidak ada error sintaks YAML

3. **Test endpoint baru**
   - Pastikan semua endpoint berfungsi
   - Update examples jika diperlukan

4. **Commit changes**
   ```bash
   git add doc/swagger/
   git commit -m "Update API documentation"
   ```

## 📚 Referensi

- [OpenAPI Specification](https://swagger.io/specification/)
- [Swagger Editor](https://editor.swagger.io/)
- [Swagger UI](https://swagger.io/tools/swagger-ui/)
- [Laravel API Resources](https://laravel.com/docs/api-resources)
- [Laravel Sanctum Authentication](https://laravel.com/docs/sanctum)

## 💡 Tips

1. **Gunakan Try it Out** di Swagger UI untuk testing cepat
2. **Bookmark dokumentasi** untuk referensi cepat saat development
3. **Update dokumentasi** setiap kali ada perubahan API
4. **Validasi response** sesuai dengan schema yang didefinisikan
5. **Test edge cases** untuk validasi error handling 