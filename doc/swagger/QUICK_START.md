# Quick Start Guide - Kbuy.me Vendor API

## 🚀 <PERSON><PERSON> dalam 5 Menit

### 1. Buka Dokumentasi
```bash
# Option A: Buka di browser
open doc/swagger/index.html

# Option B: Menggunakan live server
cd doc/swagger && python3 -m http.server 8080
```

### 2. Testing dengan Swagger UI
1. **Buka:** `http://localhost:8080` (jika menggunakan live server)
2. **Authorize:** Klik tombol "Authorize" → masukkan `Bearer YOUR_TOKEN`
3. **Test:** Pilih endpoint → "Try it out" → Execute

### 3. Import ke Postman
1. **Collection:** Import `kbuy-vendor-api.postman_collection.json`
2. **Environment:** Import `kbuy-environment.postman_environment.json`
3. **Set Token:** Edit environment → set `auth_token` value

### 4. Testing Manual dengan cURL
```bash
# Register vendor baru
curl -X POST "http://localhost:8000/api/vendor/register" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Vendor",
    "email": "<EMAIL>", 
    "phone_number": "08123456789",
    "source": "klikbuy"
  }'

# Get vendor list (dengan auth)
curl -X GET "http://localhost:8000/api/public/vendors" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🎯 Endpoint Populer

| Method | Endpoint | Fungsi |
|--------|----------|--------|
| `POST` | `/vendor/register` | Daftar vendor baru |
| `GET` | `/public/vendors` | List vendor untuk KOL |
| `POST` | `/vendor/promo` | Buat campaign baru |
| `GET` | `/vendor/promo` | List campaign vendor |
| `POST` | `/public/vendor/{id}/follow` | Follow vendor |

## 🔑 Response Format
```json
{
  "success": true,
  "message": "Operasi berhasil",
  "data": { /* response data */ }
}
```

## 📱 Base URLs
- **Development:** `http://localhost:8000/api`
- **Production:** `https://api.kbuy.me`

## 🔒 Authentication
- **Header:** `Authorization: Bearer {token}`
- **Public endpoints:** Tidak perlu auth
- **Private endpoints:** Butuh token

## 📞 Support
- **Dokumentasi lengkap:** [README.md](README.md)
- **API markdown:** [../vendor-api.md](../vendor-api.md)
- **Issue tracker:** GitHub Issues 