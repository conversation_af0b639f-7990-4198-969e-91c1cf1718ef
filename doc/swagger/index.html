<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title>Kbuy.me Vendor API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
  <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@4.15.5/favicon-32x32.png" sizes="32x32" />
  <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@4.15.5/favicon-16x16.png" sizes="16x16" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }

    *, *:before, *:after {
      box-sizing: inherit;
    }

    body {
      margin:0;
      background: #fafafa;
    }

    .topbar {
      background: #1b1b1b !important;
      padding: 20px 0;
    }

    .topbar .topbar-wrapper {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    .topbar .link {
      color: #fff;
      text-decoration: none;
      font-size: 1.5em;
      font-weight: bold;
    }

    .custom-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px 0;
      text-align: center;
      margin-bottom: 20px;
    }

    .custom-header h1 {
      margin: 0;
      font-size: 2.5em;
      font-weight: 300;
    }

    .custom-header p {
      margin: 10px 0 0 0;
      font-size: 1.2em;
      opacity: 0.9;
    }

    .info-box {
      background: #e3f2fd;
      border: 1px solid #2196f3;
      border-radius: 8px;
      padding: 20px;
      margin: 20px auto;
      max-width: 1200px;
    }

    .info-box h3 {
      color: #1976d2;
      margin-top: 0;
    }

    .button-group {
      margin: 20px auto;
      text-align: center;
      max-width: 1200px;
    }

    .btn {
      display: inline-block;
      padding: 12px 24px;
      margin: 0 10px;
      background: #2196f3;
      color: white;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 500;
      transition: background 0.3s;
    }

    .btn:hover {
      background: #1976d2;
    }

    .btn.secondary {
      background: #4caf50;
    }

    .btn.secondary:hover {
      background: #388e3c;
    }
  </style>
</head>

<body>
  <div class="custom-header">
    <h1>Kbuy.me Vendor API</h1>
    <p>Dokumentasi lengkap API untuk manajemen vendor, promo, marketing kit, dan notifikasi</p>
  </div>

  <div class="info-box">
    <h3>📋 Informasi API</h3>
    <p><strong>Base URL Development:</strong> <code>http://localhost:8000/api</code></p>
    <p><strong>Base URL Production:</strong> <code>https://api.kbuy.me</code></p>
    <p><strong>Authentication:</strong> Bearer Token (Laravel Sanctum)</p>
    <p><strong>Format Response:</strong> JSON dengan standar <code>{"success": boolean, "message": string, "data": object}</code></p>
  </div>

  <div class="button-group">
    <a href="vendor-api.yaml" class="btn" download>📥 Download YAML</a>
    <a href="kbuy-vendor-api.postman_collection.json" class="btn secondary" download>📮 Download Postman Collection</a>
    <a href="kbuy-environment.postman_environment.json" class="btn secondary" download>🔧 Download Environment</a>
  </div>

  <div id="swagger-ui"></div>

  <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      // Begin Swagger UI call region
      const ui = SwaggerUIBundle({
        url: './vendor-api.yaml',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        tryItOutEnabled: true,
        requestInterceptor: function(request) {
          // Log request untuk debugging
          console.log('API Request:', request);
          return request;
        },
        responseInterceptor: function(response) {
          // Log response untuk debugging
          console.log('API Response:', response);
          return response;
        },
        onComplete: function() {
          console.log('Swagger UI berhasil dimuat!');
        },
        defaultModelsExpandDepth: 2,
        defaultModelExpandDepth: 2,
        docExpansion: 'list',
        operationsSorter: 'alpha',
        tagsSorter: 'alpha',
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        validatorUrl: null
      });
      // End Swagger UI call region

      window.ui = ui;
    };
  </script>

  <div style="margin: 40px auto; max-width: 1200px; padding: 20px; background: #f5f5f5; border-radius: 8px;">
    <h3>🚀 Cara Menggunakan</h3>
    <ol>
      <li><strong>Authentication:</strong> Klik tombol "Authorize" dan masukkan Bearer token</li>
      <li><strong>Testing:</strong> Pilih endpoint, isi parameter, dan klik "Try it out"</li>
      <li><strong>Development:</strong> Copy curl command untuk implementasi</li>
      <li><strong>Postman:</strong> Download collection dan environment untuk testing lengkap</li>
    </ol>
    
    <h3>📊 Endpoint Summary</h3>
    <ul>
      <li><strong>Vendor Management:</strong> 5 endpoints untuk registrasi dan manajemen profil</li>
      <li><strong>Promo Management:</strong> 5 endpoints untuk CRUD promo/campaign</li>
      <li><strong>Marketing Kit:</strong> 3 endpoints untuk manajemen material marketing</li>
      <li><strong>Public Vendor:</strong> 3 endpoints untuk KOL browse vendor</li>
      <li><strong>Follow & Review:</strong> 4 endpoints untuk sistem follow dan review</li>
      <li><strong>Notifikasi:</strong> 3 endpoints untuk sistem notifikasi KOL</li>
    </ul>

    <h3>🔗 Resources</h3>
    <ul>
      <li><a href="https://swagger.io/docs/" target="_blank">Swagger Documentation</a></li>
      <li><a href="https://laravel.com/docs/sanctum" target="_blank">Laravel Sanctum Auth</a></li>
      <li><a href="../vendor-api.md" target="_blank">Markdown Documentation</a></li>
    </ul>
  </div>

  <footer style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #eee; margin-top: 40px;">
    <p>&copy; 2025 Kbuy.me - Platform Tracking & Komisi KOL</p>
    <p>Generated on: <script>document.write(new Date().toLocaleDateString('id-ID'));</script></p>
  </footer>

</body>
</html> 