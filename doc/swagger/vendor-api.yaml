openapi: 3.0.3
info:
  title: Kbuy.me Vendor API
  description: API untuk manajemen vendor, promo, marketing kit, follow, review, dan notifikasi di platform Kbuy.me
  version: 1.0.0
  contact:
    name: Kbuy.me Development Team
    email: <EMAIL>

servers:
  - url: http://localhost:8000/api
    description: Development Server
  - url: https://api.kbuy.me
    description: Production Server

security:
  - BearerAuth: []

paths:
  # ===== VENDOR MANAGEMENT =====
  /vendor/register:
    post:
      tags:
        - Vendor Management
      summary: Registrasi vendor baru
      description: Endpoint untuk registrasi vendor baru (server-to-server)
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - email
                - phone_number
                - source
              properties:
                name:
                  type: string
                  maxLength: 150
                  example: "Skincare ABC"
                email:
                  type: string
                  format: email
                  maxLength: 150
                  example: "<EMAIL>"
                phone_number:
                  type: string
                  maxLength: 20
                  example: "08123456789"
                source:
                  type: string
                  enum: [gass, klikbuy]
                  example: "klikbuy"
      responses:
        '200':
          description: Vendor berhasil didaftarkan
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Vendor berhasil didaftarkan"
                  data:
                    type: object
                    example:
                      id: 1
                      name: "Skincare ABC"
                      email: "<EMAIL>"
                      phone_number: "08123456789"
                      source: "klikbuy"
                      status: 1
                      created_at: "2025-01-07T10:00:00Z"
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Validasi gagal"
                  data:
                    type: object
                    nullable: true
                    example: null

  /vendor/check-id:
    get:
      tags:
        - Vendor Management
      summary: Cek ID vendor
      description: Cek apakah vendor id/email/phone sudah terdaftar
      security: []
      parameters:
        - name: email
          in: query
          description: Email vendor
          schema:
            type: string
            format: email
        - name: phone_number
          in: query
          description: Nomor HP vendor
          schema:
            type: string
        - name: id
          in: query
          description: ID vendor
          schema:
            type: integer
      responses:
        '200':
          description: Status ketersediaan ID/email/phone
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Sudah terdaftar"
                  data:
                    type: object
                    properties:
                      exists:
                        type: boolean
                        example: true

  /vendor:
    get:
      tags:
        - Vendor Management
      summary: Get profile vendor
      description: Ambil profil vendor yang sedang login
      responses:
        '200':
          description: Profil vendor
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Profil vendor"
                  data:
                    type: object
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Unauthorized"
                  data:
                    type: object
                    nullable: true
                    example: null

    put:
      tags:
        - Vendor Management
      summary: Update profile vendor
      description: Update profil vendor
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                about:
                  type: string
                  example: "Brand skincare terpercaya dengan bahan alami"
                profile_pic_url:
                  type: string
                  format: url
                  example: "https://example.com/logo.jpg"
                instagram_url:
                  type: string
                  format: url
                tiktok_url:
                  type: string
                  format: url
                website_url:
                  type: string
                  format: url
                address:
                  type: string
                  maxLength: 255
                brand_category:
                  type: string
                  maxLength: 50
                  example: "kecantikan"
                business_type:
                  type: string
                  maxLength: 50
                  example: "B2C"
      responses:
        '200':
          description: Profil berhasil diupdate
        '401':
          description: Unauthorized

    delete:
      tags:
        - Vendor Management
      summary: Hapus akun vendor
      description: Hapus akun vendor
      responses:
        '200':
          description: Akun berhasil dihapus
        '401':
          description: Unauthorized

  # ===== PROMO MANAGEMENT =====
  /vendor/promo:
    post:
      tags:
        - Promo Management
      summary: Buat promo baru
      description: Membuat promo/campaign baru
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - nama_promo
                - start_date
                - end_date
                - budget_limit
                - commission_model
                - target_type
                - target_value
                - komisi_type
              properties:
                nama_promo:
                  type: string
                  maxLength: 200
                  example: "Promo Skincare Ramadan"
                deskripsi:
                  type: string
                start_date:
                  type: string
                  format: date-time
                end_date:
                  type: string
                  format: date-time
                budget_limit:
                  type: number
                  minimum: 50000
                commission_model:
                  type: string
                  enum: [single, multi]
                target_type:
                  type: string
                  enum: [whatsapp, url]
                target_value:
                  type: string
                max_closing:
                  type: integer
                  minimum: 1
                komisi_type:
                  type: string
                  enum: [fixed, percentage]
                komisi_per_lead:
                  type: number
                  minimum: 0
                komisi_per_mql:
                  type: number
                  minimum: 0
                komisi_per_prospek:
                  type: number
                  minimum: 0
                komisi_per_closing:
                  type: number
                  minimum: 0
      responses:
        '200':
          description: Promo berhasil dibuat
        '400':
          description: Bad Request
        '401':
          description: Unauthorized

    get:
      tags:
        - Promo Management
      summary: List promo vendor
      description: Daftar promo milik vendor dengan filter
      parameters:
        - name: status
          in: query
          description: Status promo
          schema:
            type: integer
            enum: [0, 1, 2, 3, 4]
        - name: start_date
          in: query
          description: Filter tanggal mulai
          schema:
            type: string
            format: date
        - name: budget_remaining
          in: query
          description: Filter sisa budget minimal
          schema:
            type: number
      responses:
        '200':
          description: Daftar promo
        '401':
          description: Unauthorized

  /vendor/promo/{id}:
    get:
      tags:
        - Promo Management
      summary: Detail promo
      description: Detail promo tertentu
      parameters:
        - name: id
          in: path
          required: true
          description: ID promo
          schema:
            type: integer
      responses:
        '200':
          description: Detail promo
        '404':
          description: Not Found

    put:
      tags:
        - Promo Management
      summary: Update promo
      description: Update promo yang sudah ada
      parameters:
        - name: id
          in: path
          required: true
          description: ID promo
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                nama_promo:
                  type: string
                  maxLength: 200
                deskripsi:
                  type: string
                start_date:
                  type: string
                  format: date-time
                end_date:
                  type: string
                  format: date-time
                budget_limit:
                  type: number
                  minimum: 50000
                commission_model:
                  type: string
                  enum: [single, multi]
                target_type:
                  type: string
                  enum: [whatsapp, url]
                target_value:
                  type: string
                max_closing:
                  type: integer
                  minimum: 1
                komisi_type:
                  type: string
                  enum: [fixed, percentage]
                komisi_per_lead:
                  type: number
                  minimum: 0
                komisi_per_mql:
                  type: number
                  minimum: 0
                komisi_per_prospek:
                  type: number
                  minimum: 0
                komisi_per_closing:
                  type: number
                  minimum: 0
      responses:
        '200':
          description: Promo berhasil diupdate
        '404':
          description: Not Found

    delete:
      tags:
        - Promo Management
      summary: Hapus promo
      description: Hapus promo
      parameters:
        - name: id
          in: path
          required: true
          description: ID promo
          schema:
            type: integer
      responses:
        '200':
          description: Promo berhasil dihapus
        '404':
          description: Not Found

  # ===== MARKETING KIT MANAGEMENT =====
  /vendor/promo/{promo_id}/marketing-kit:
    post:
      tags:
        - Marketing Kit
      summary: Tambah marketing kit
      description: Tambah marketing kit ke promo
      parameters:
        - name: promo_id
          in: path
          required: true
          description: ID promo
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - nama_kit
                - tipe
                - url_file
              properties:
                nama_kit:
                  type: string
                  maxLength: 200
                  example: "Banner Promo Ramadan"
                tipe:
                  type: string
                  enum: [image, video, pdf, link]
                  example: "image"
                url_file:
                  type: string
                  format: url
                  example: "https://example.com/banner.jpg"
                file_size:
                  type: integer
                  minimum: 0
                  example: 512000
      responses:
        '200':
          description: Marketing kit berhasil ditambahkan

    get:
      tags:
        - Marketing Kit
      summary: List marketing kit
      description: Daftar marketing kit promo
      parameters:
        - name: promo_id
          in: path
          required: true
          description: ID promo
          schema:
            type: integer
      responses:
        '200':
          description: Daftar marketing kit

  /vendor/marketing-kit/{id}:
    delete:
      tags:
        - Marketing Kit
      summary: Hapus marketing kit
      description: Hapus marketing kit
      parameters:
        - name: id
          in: path
          required: true
          description: ID marketing kit
          schema:
            type: integer
      responses:
        '200':
          description: Marketing kit berhasil dihapus

  # ===== PUBLIC ENDPOINTS =====
  /public/vendors:
    get:
      tags:
        - Public - Vendor
      summary: List vendor public
      description: Daftar semua vendor untuk KOL
      security: []
      parameters:
        - name: brand_category
          in: query
          description: Filter kategori brand
          schema:
            type: string
        - name: min_rating
          in: query
          description: Rating minimal
          schema:
            type: number
            minimum: 1
            maximum: 5
        - name: is_verified
          in: query
          description: Status verifikasi
          schema:
            type: integer
            enum: [0, 1]
        - name: sort
          in: query
          description: Field untuk sorting
          schema:
            type: string
            enum: [rating, total_kol_partners, avg_commission_amount, created_at]
        - name: order
          in: query
          description: Urutan sorting
          schema:
            type: string
            enum: [asc, desc]
      responses:
        '200':
          description: Daftar vendor (paginated)
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Daftar vendor"
                  data:
                    type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                      current_page:
                        type: integer
                        example: 1
                      per_page:
                        type: integer
                        example: 15
                      total:
                        type: integer
                        example: 100
                      last_page:
                        type: integer
                        example: 7

  /public/vendor/{id}:
    get:
      tags:
        - Public - Vendor
      summary: Detail vendor
      description: Detail lengkap vendor untuk KOL
      security: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID vendor
          schema:
            type: integer
      responses:
        '200':
          description: Detail vendor

  /public/vendors/search:
    get:
      tags:
        - Public - Vendor
      summary: Search vendor
      description: Search vendor dengan multiple criteria
      security: []
      parameters:
        - name: keyword
          in: query
          description: Keyword pencarian
          schema:
            type: string
        - name: category
          in: query
          description: Kategori brand
          schema:
            type: string
        - name: min_commission
          in: query
          description: Komisi minimal
          schema:
            type: number
        - name: max_commission
          in: query
          description: Komisi maksimal
          schema:
            type: number
        - name: min_rating
          in: query
          description: Rating minimal
          schema:
            type: number
        - name: location
          in: query
          description: Lokasi
          schema:
            type: string
        - name: business_type
          in: query
          description: Tipe bisnis
          schema:
            type: string
        - name: is_verified
          in: query
          description: Status verifikasi
          schema:
            type: integer
            enum: [0, 1]
      responses:
        '200':
          description: Hasil pencarian vendor

  # ===== FOLLOW & REVIEW =====
  /public/vendor/{id}/follow:
    post:
      tags:
        - Follow & Review
      summary: Follow vendor
      description: KOL follow vendor
      parameters:
        - name: id
          in: path
          required: true
          description: ID vendor
          schema:
            type: integer
      responses:
        '200':
          description: Berhasil follow vendor

    delete:
      tags:
        - Follow & Review
      summary: Unfollow vendor
      description: KOL unfollow vendor
      parameters:
        - name: id
          in: path
          required: true
          description: ID vendor
          schema:
            type: integer
      responses:
        '200':
          description: Berhasil unfollow vendor

  /public/vendor/{id}/review:
    post:
      tags:
        - Follow & Review
      summary: Buat review vendor
      description: KOL kasih review ke vendor
      parameters:
        - name: id
          in: path
          required: true
          description: ID vendor
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - rating
              properties:
                rating:
                  type: integer
                  minimum: 1
                  maximum: 5
                  example: 5
                review:
                  type: string
                  example: "Vendor yang sangat recommended!"
                campaign_id:
                  type: integer
                  example: 123
      responses:
        '200':
          description: Review berhasil ditambahkan

    get:
      tags:
        - Follow & Review
      summary: List review vendor
      description: Daftar review dari KOL lain
      security: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID vendor
          schema:
            type: integer
        - name: rating
          in: query
          description: Filter rating
          schema:
            type: integer
            minimum: 1
            maximum: 5
        - name: is_verified
          in: query
          description: Filter status verifikasi
          schema:
            type: integer
            enum: [0, 1]
      responses:
        '200':
          description: Daftar review

  # ===== NOTIFICATIONS =====
  /kol/notifications:
    get:
      tags:
        - Notifikasi
      summary: List notifikasi
      description: Daftar notifikasi untuk KOL
      parameters:
        - name: is_read
          in: query
          description: Status baca
          schema:
            type: integer
            enum: [0, 1]
        - name: type
          in: query
          description: Tipe notifikasi
          schema:
            type: string
            enum: [new_campaign, campaign_update, campaign_ended]
        - name: date_from
          in: query
          description: Tanggal dari
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          description: Tanggal sampai
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Daftar notifikasi

  /kol/notifications/{id}/read:
    put:
      tags:
        - Notifikasi
      summary: Mark notifikasi read
      description: Tandai notifikasi sudah dibaca
      parameters:
        - name: id
          in: path
          required: true
          description: ID notifikasi
          schema:
            type: integer
      responses:
        '200':
          description: Notifikasi ditandai sudah dibaca

  /kol/notifications/mark-all-read:
    put:
      tags:
        - Notifikasi
      summary: Mark all read
      description: Tandai semua notifikasi sudah dibaca
      responses:
        '200':
          description: Semua notifikasi ditandai sudah dibaca

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

tags:
  - name: Vendor Management
    description: Operasi manajemen vendor
  - name: Promo Management
    description: Operasi manajemen promo/campaign
  - name: Marketing Kit
    description: Operasi manajemen marketing kit
  - name: Public - Vendor
    description: Endpoint publik untuk KOL melihat vendor
  - name: Follow & Review
    description: Sistem follow dan review vendor
  - name: Notifikasi
    description: Sistem notifikasi untuk KOL
