{"info": {"name": "Kbuy.me Vendor API", "description": "Collection untuk testing API Vendor platform Kbuy.me", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "vendor_id", "value": "", "type": "string"}, {"key": "promo_id", "value": "", "type": "string"}], "item": [{"name": "Vendor Management", "item": [{"name": "Register Vendor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Skincare ABC\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"08123456789\",\n  \"source\": \"klikbuy\"\n}"}, "url": {"raw": "{{base_url}}/vendor/register", "host": ["{{base_url}}"], "path": ["vendor", "register"]}}}, {"name": "Check Vendor ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/vendor/check-id?email=<EMAIL>", "host": ["{{base_url}}"], "path": ["vendor", "check-id"], "query": [{"key": "email", "value": "<EMAIL>"}]}}}, {"name": "Get Vendor Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vendor", "host": ["{{base_url}}"], "path": ["vendor"]}}}]}]}