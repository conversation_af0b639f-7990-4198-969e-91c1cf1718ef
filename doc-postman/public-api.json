{"info": {"name": "Public API - Kbuy.me", "description": "Collection untuk Public endpoints - vendor list, search, performance tracking", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}], "item": [{"name": "🌍 Public Vendor", "item": [{"name": "List All Vendors", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/vendors?brand_category=kecantikan&min_rating=4&is_verified=1", "host": ["{{base_url}}"], "path": ["public", "vendors"], "query": [{"key": "brand_category", "value": "kecantikan"}, {"key": "min_rating", "value": "4"}, {"key": "is_verified", "value": "1"}]}, "description": "List semua vendor untuk KOL dengan filter"}}, {"name": "Get Vendor <PERSON>ail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/vendor/1", "host": ["{{base_url}}"], "path": ["public", "vendor", "1"]}, "description": "Detail lengkap vendor untuk KOL"}}, {"name": "Get Vendor Promos", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/vendor/1/promos", "host": ["{{base_url}}"], "path": ["public", "vendor", "1", "promos"]}, "description": "List promo aktif dari vendor tertentu"}}, {"name": "Search Vendors", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/vendors/search?keyword=skincare&category=kecantikan&min_commission=10&max_commission=25", "host": ["{{base_url}}"], "path": ["public", "vendors", "search"], "query": [{"key": "keyword", "value": "skincare"}, {"key": "category", "value": "kecantikan"}, {"key": "min_commission", "value": "10"}, {"key": "max_commission", "value": "25"}]}, "description": "Search vendor dengan multiple criteria"}}, {"name": "Recommended Vend<PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/vendors/recommended", "host": ["{{base_url}}"], "path": ["public", "vendors", "recommended"]}, "description": "Rekomendasi vendor untuk KOL berdasarkan profile"}}, {"name": "Trending Vendors", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/vendors/trending", "host": ["{{base_url}}"], "path": ["public", "vendors", "trending"]}, "description": "Vendor yang sedang trending"}}, {"name": "New Vendors", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/vendors/new", "host": ["{{base_url}}"], "path": ["public", "vendors", "new"]}, "description": "Vendor baru yang baru join platform"}}]}, {"name": "📊 Performance Tracking", "item": [{"name": "Live Performance Vendor", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/vendor/1/live-performance", "host": ["{{base_url}}"], "path": ["public", "vendor", "1", "live-performance"]}, "description": "Live performance data vendor hari ini"}}, {"name": "Promo Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/promo/1/analytics", "host": ["{{base_url}}"], "path": ["public", "promo", "1", "analytics"]}, "description": "Analytics promo tertentu"}}, {"name": "Vendor Performance History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/vendor/1/performance-history", "host": ["{{base_url}}"], "path": ["public", "vendor", "1", "performance-history"]}, "description": "History performa vendor per bulan"}}, {"name": "Campaign Leaderboard", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/campaign-leaderboard", "host": ["{{base_url}}"], "path": ["public", "campaign-leaderboard"]}, "description": "Leaderboard campaign/vendor terpopuler"}}]}]}