{"info": {"name": "User API - Kbuy.me", "description": "Collection untuk Auth & User Management", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["if (!pm.collectionVariables.get('base_url')) {", "    pm.collectionVariables.set('base_url', 'http://localhost:8000/api');", "}"], "type": "text/javascript"}}], "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "🔐 Auth & Verifikasi", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"***********\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/register", "host": ["{{base_url}}"], "path": ["register"]}, "description": "Registrasi user baru (KOL)"}}, {"name": "Request OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone_number\": \"***********\",\n  \"type\": \"register\"\n}"}, "url": {"raw": "{{base_url}}/request-otp", "host": ["{{base_url}}"], "path": ["request-otp"]}, "description": "Request OTP WhatsApp untuk verifikasi"}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone_number\": \"***********\",\n  \"otp_code\": \"123456\",\n  \"type\": \"register\"\n}"}, "url": {"raw": "{{base_url}}/verify-otp", "host": ["{{base_url}}"], "path": ["verify-otp"]}, "description": "Verifikasi OTP WhatsApp"}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"email_verification_token_here\"\n}"}, "url": {"raw": "{{base_url}}/email/verify", "host": ["{{base_url}}"], "path": ["email", "verify"]}, "description": "Verifikasi email via token"}}, {"name": "Resend Email Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/email/resend", "host": ["{{base_url}}"], "path": ["email", "resend"]}, "description": "<PERSON><PERSON> link verifikasi email"}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('auth_token', response.token);", "        console.log('<PERSON><PERSON> saved:', response.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}, "description": "Login user dengan email/phone dan password"}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/logout", "host": ["{{base_url}}"], "path": ["logout"]}, "description": "Logout user dan revoke token"}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/forgot-password", "host": ["{{base_url}}"], "path": ["forgot-password"]}, "description": "Request reset password"}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset_token_here\",\n  \"password\": \"newpassword123\",\n  \"password_confirmation\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/reset-password", "host": ["{{base_url}}"], "path": ["reset-password"]}, "description": "Reset password dengan token"}}]}, {"name": "👤 User Management", "item": [{"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}, "description": "Get profil user yang sedang login"}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"***********\",\n  \"city\": \"Jakarta\",\n  \"country\": \"Indonesia\"\n}"}, "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}, "description": "Update profil user"}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}, "description": "<PERSON><PERSON> akun user"}}, {"name": "Update Payment Info", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"tujuan_pembayaran\": [\n    {\n      \"type\": \"bank\",\n      \"bank_name\": \"BCA\",\n      \"account_number\": \"**********\",\n      \"account_name\": \"<PERSON>\"\n    },\n    {\n      \"type\": \"ewallet\",\n      \"provider\": \"gopay\",\n      \"phone_number\": \"***********\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/user/payment", "host": ["{{base_url}}"], "path": ["user", "payment"]}, "description": "Update tujuan pembayaran komisi"}}, {"name": "Update Social Media", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"social_media\": [\n    {\n      \"platform\": \"instagram\",\n      \"username_or_url\": \"@johndoe\"\n    },\n    {\n      \"platform\": \"tiktok\",\n      \"username_or_url\": \"@johndoe_tiktok\"\n    },\n    {\n      \"platform\": \"youtube\",\n      \"username_or_url\": \"https://youtube.com/@johndoe\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/user/social-media", "host": ["{{base_url}}"], "path": ["user", "social-media"]}, "description": "Update daftar social media user"}}]}]}