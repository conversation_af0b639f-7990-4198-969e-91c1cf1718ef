{"info": {"name": "KOL API - Kbuy.me", "description": "Collection untuk KOL - notifikasi, feed, follow/review system", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "🔔 <PERSON><PERSON><PERSON><PERSON>", "item": [{"name": "List Notifikasi", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/kol/notifications?is_read=0&type=new_campaign", "host": ["{{base_url}}"], "path": ["kol", "notifications"], "query": [{"key": "is_read", "value": "0"}, {"key": "type", "value": "new_campaign"}]}, "description": "List notifikasi untuk KOL"}}, {"name": "<PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/kol/notifications/1/read", "host": ["{{base_url}}"], "path": ["kol", "notifications", "1", "read"]}, "description": "Tandai notifikasi sudah dibaca"}}, {"name": "<PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/kol/notifications/mark-all-read", "host": ["{{base_url}}"], "path": ["kol", "notifications", "mark-all-read"]}, "description": "Tandai semua notifikasi sudah dibaca"}}, {"name": "Delete Notifikasi", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/kol/notifications/1", "host": ["{{base_url}}"], "path": ["kol", "notifications", "1"]}, "description": "<PERSON><PERSON> notif<PERSON>"}}]}, {"name": "📱 Feed & Timeline", "item": [{"name": "Get Feed", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/kol/feed", "host": ["{{base_url}}"], "path": ["kol", "feed"]}, "description": "Feed timeline campaign baru/update untuk KOL"}}, {"name": "<PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/kol/feed/1/seen", "host": ["{{base_url}}"], "path": ["kol", "feed", "1", "seen"]}, "description": "Tandai feed item sudah dilihat"}}, {"name": "Unread Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/kol/feed/unread-count", "host": ["{{base_url}}"], "path": ["kol", "feed", "unread-count"]}, "description": "Count not<PERSON><PERSON><PERSON> dan feed yang belum dibaca"}}]}, {"name": "👥 Follow & Review", "item": [{"name": "Follow <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/public/vendor/1/follow", "host": ["{{base_url}}"], "path": ["public", "vendor", "1", "follow"]}, "description": "KOL follow vendor"}}, {"name": "Unfollow Vendor", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/public/vendor/1/follow", "host": ["{{base_url}}"], "path": ["public", "vendor", "1", "follow"]}, "description": "KOL unfollow vendor"}}, {"name": "List Followed Vendors", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/public/kol/1/followed-vendors", "host": ["{{base_url}}"], "path": ["public", "kol", "1", "followed-vendors"]}, "description": "List vendor yang di-follow KOL"}}, {"name": "Create Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 5,\n  \"review\": \"Vendor yang sangat recommended, payment cepat dan campaign menarik!\",\n  \"campaign_id\": 123\n}"}, "url": {"raw": "{{base_url}}/public/vendor/1/review", "host": ["{{base_url}}"], "path": ["public", "vendor", "1", "review"]}, "description": "KOL kasih review ke vendor"}}, {"name": "List Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/public/vendor/1/reviews?rating=5&is_verified=1", "host": ["{{base_url}}"], "path": ["public", "vendor", "1", "reviews"], "query": [{"key": "rating", "value": "5"}, {"key": "is_verified", "value": "1"}]}, "description": "List review dari KOL lain"}}, {"name": "Update Review", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 4,\n  \"review\": \"Updated review: Vendor bagus tapi ada room for improvement\"\n}"}, "url": {"raw": "{{base_url}}/public/vendor/1/review/1", "host": ["{{base_url}}"], "path": ["public", "vendor", "1", "review", "1"]}, "description": "Update review KOL"}}, {"name": "Delete Review", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/public/vendor/1/review/1", "host": ["{{base_url}}"], "path": ["public", "vendor", "1", "review", "1"]}, "description": "Hapus review KOL"}}]}]}