# Postman Collections - Kbuy.me API

Collection Postman untuk API platform Kbuy.me sudah dipecah berdasarkan kategori untuk memudahkan navigasi dan penggunaan.

## 📁 File Collections

### 1. `user-api.json` - User & Authentication
**Kategori:** Auth & User Management  
**Endpoints:**
- 🔐 **Auth & Verifikasi**: Register, Login, OTP, Email verification, Password reset
- 👤 **User Management**: Profile, Update data, Payment info, Social media

**Fitur khusus:**
- Auto-save token setelah login
- Environment variables untuk base URL dan token

---

### 2. `vendor-api.json` - Vendor Management
**Kategori:** Vendor, Promo & Marketing Kit  
**Endpoints:**
- 🏢 **Vendor Management**: Register vendor, Profile management
- 📢 **Promo Management**: CRUD promo, Pause/extend, Budget tracking
- 🎨 **Marketing Kit**: Upload, List, Delete marketing materials

**Auth required:** Bearer token untuk sebagian besar endpoints

---

### 3. `public-api.json` - Public Endpoints
**Kategori:** Public data & Search  
**Endpoints:**
- 🌍 **Public Vendor**: List vendors, Search, Recommendations, Trending
- 📊 **Performance Tracking**: Live performance, Analytics, Leaderboard

**Auth required:** Tidak (semua public)

---

### 4. `kol-api.json` - KOL Features
**Kategori:** KOL-specific features  
**Endpoints:**
- 🔔 **Notifikasi**: List, Mark read, Delete notifications
- 📱 **Feed & Timeline**: Campaign feeds, Mark seen, Unread count
- 👥 **Follow & Review**: Follow vendors, Create reviews, Manage interactions

**Auth required:** Bearer token untuk semua endpoints

---

### 5. `system-api.json` - Internal System
**Kategori:** Automation & Monitoring  
**Endpoints:**
- 🤖 **Campaign Automation**: Budget checks, Auto-stop campaigns
- 📧 **Notifications**: Send campaign notifications  
- 📊 **Stats Management**: Update vendor statistics

**Auth required:** Internal API key (`X-Internal-Token`)

---

## 🚀 Cara Import ke Postman

### Import Collection
1. Buka Postman
2. Click **Import** di pojok kiri atas
3. Pilih **File** tab
4. Drag & drop atau browse file `.json` yang ingin diimport
5. Click **Import**

### Setup Environment Variables
Setelah import, set variable berikut:

| Variable | Value | Description |
|----------|--------|-------------|
| `base_url` | `http://localhost:8000/api` | Base URL API (development) |
| `auth_token` | `your_token_here` | Bearer token setelah login |
| `internal_token` | `your_internal_key` | Internal API key (system-api only) |

### Production Environment
Untuk production, ganti `base_url` menjadi:
```
https://api.kbuy.me
```

---

## 📝 Workflow Penggunaan

### 1. Mulai dengan User API
- Import `user-api.json`
- Gunakan endpoint **Login** untuk mendapatkan token
- Token akan otomatis tersimpan di variable `auth_token`

### 2. Test Vendor Features  
- Import `vendor-api.json`
- Gunakan token dari step 1 untuk akses endpoint vendor
- Test CRUD promo dan marketing kit

### 3. Explore Public Data
- Import `public-api.json` 
- Test search vendor, recommendations, performance tracking
- Tidak perlu authentication

### 4. KOL Interactions
- Import `kol-api.json`
- Test notifikasi, feed, follow/review system
- Gunakan token dari user login

### 5. System Monitoring (Optional)
- Import `system-api.json` untuk internal automation
- Perlu internal API key yang berbeda

---

## 🔧 Tips Penggunaan

### Shared Variables
Semua collection menggunakan variable yang sama:
- `{{base_url}}` - URL API base
- `{{auth_token}}` - Token authentication

### Auto-Authentication
Collection sudah dikonfigurasi dengan:
- Bearer token authentication untuk user/vendor/kol APIs
- API key authentication untuk system API

### Testing Flow
1. **Login** → auto-save token
2. **Get Profile** → verify token works  
3. **Test specific features** → sesuai kebutuhan
4. **Logout** → clear session

### Error Handling
Semua endpoint menggunakan format response standar:
```json
{
  "success": true/false,
  "message": "Response message",
  "data": { /* response data */ }
}
```

---

## 📋 Checklist Import

- [ ] `user-api.json` - Basic auth & user management
- [ ] `vendor-api.json` - Vendor & promo features  
- [ ] `public-api.json` - Public data & search
- [ ] `kol-api.json` - KOL-specific features
- [ ] `system-api.json` - Internal automation (optional)

Setelah import semua, kamu siap untuk testing API lengkap platform Kbuy.me! 🎉 