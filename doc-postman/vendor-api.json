{"info": {"name": "Vendor API - Kbuy.me", "description": "Collection untuk Vendor Management, Promo & Marketing Kit", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "🏢 Vendor Management", "item": [{"name": "Register Vendor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Skincare ABC\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"08123456789\",\n  \"source\": \"klikbuy\"\n}"}, "url": {"raw": "{{base_url}}/vendor/register", "host": ["{{base_url}}"], "path": ["vendor", "register"]}, "description": "Registrasi vendor baru (tidak perlu auth)"}}, {"name": "Check ID Vendor", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/vendor/check-id?email=<EMAIL>", "host": ["{{base_url}}"], "path": ["vendor", "check-id"], "query": [{"key": "email", "value": "<EMAIL>"}]}, "description": "Cek apakah vendor sudah terdaftar"}}, {"name": "Get Profile Vendor", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vendor", "host": ["{{base_url}}"], "path": ["vendor"]}, "description": "Get profil vendor yang sedang login"}}, {"name": "Update Profile Vendor", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"about\": \"Brand skincare terpercaya sejak 2020\",\n  \"profile_pic_url\": \"https://example.com/logo.jpg\",\n  \"brand_category\": \"kecantikan\",\n  \"business_type\": \"B2C\",\n  \"city\": \"Jakarta\",\n  \"country\": \"Indonesia\"\n}"}, "url": {"raw": "{{base_url}}/vendor", "host": ["{{base_url}}"], "path": ["vendor"]}, "description": "Update profil vendor"}}, {"name": "Delete Vendor Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vendor", "host": ["{{base_url}}"], "path": ["vendor"]}, "description": "Hapus akun vendor"}}, {"name": "Dashboard Alerts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vendor/dashboard/alerts", "host": ["{{base_url}}"], "path": ["vendor", "dashboard", "alerts"]}, "description": "Dashboard alerts untuk vendor"}}]}, {"name": "📢 Promo Management", "item": [{"name": "Create Promo", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Flash Sale Skincare Premium\",\n  \"description\": \"Dapatkan diskon hingga 50% untuk semua produk skincare premium kami\",\n  \"target_url\": \"https://wa.me/************?text=Halo%20saya%20tertarik%20dengan%20promo%20skincare\",\n  \"commission_percentage\": 15,\n  \"commission_flat\": 0,\n  \"budget_total\": 1000000,\n  \"start_date\": \"2024-01-01\",\n  \"end_date\": \"2024-01-31\",\n  \"max_kol_count\": 50,\n  \"categories\": [\"kecantikan\", \"skincare\"]\n}"}, "url": {"raw": "{{base_url}}/vendor/promo", "host": ["{{base_url}}"], "path": ["vendor", "promo"]}, "description": "Buat promo/campaign baru"}}, {"name": "List Promo", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vendor/promo?status=active&page=1", "host": ["{{base_url}}"], "path": ["vendor", "promo"], "query": [{"key": "status", "value": "active"}, {"key": "page", "value": "1"}]}, "description": "List semua promo milik vendor"}}, {"name": "Get Promo Detail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vendor/promo/1", "host": ["{{base_url}}"], "path": ["vendor", "promo", "1"]}, "description": "Detail promo tertentu"}}, {"name": "Update Promo", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Flash Sale Skincare Premium - Updated\",\n  \"description\": \"Dapatkan diskon hingga 60% untuk semua produk skincare premium kami\",\n  \"commission_percentage\": 20,\n  \"budget_total\": 1500000\n}"}, "url": {"raw": "{{base_url}}/vendor/promo/1", "host": ["{{base_url}}"], "path": ["vendor", "promo", "1"]}, "description": "Update promo yang sudah ada"}}, {"name": "Delete Promo", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vendor/promo/1", "host": ["{{base_url}}"], "path": ["vendor", "promo", "1"]}, "description": "Hapus promo"}}, {"name": "Pause Promo", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Maintenance sistem\"\n}"}, "url": {"raw": "{{base_url}}/vendor/promo/1/pause", "host": ["{{base_url}}"], "path": ["vendor", "promo", "1", "pause"]}, "description": "Pause/resume promo sementara"}}, {"name": "Extend Promo", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"new_end_date\": \"2024-02-15\",\n  \"additional_budget\": 500000\n}"}, "url": {"raw": "{{base_url}}/vendor/promo/1/extend", "host": ["{{base_url}}"], "path": ["vendor", "promo", "1", "extend"]}, "description": "Perpanjang promo dan tambah budget"}}, {"name": "Budget Tracking", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vendor/promo/1/budget-tracking", "host": ["{{base_url}}"], "path": ["vendor", "promo", "1", "budget-tracking"]}, "description": "Tracking penggunaan budget promo real-time"}}]}, {"name": "🎨 Marketing Kit", "item": [{"name": "Create Marketing Kit", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Banner Instagram Story\",\n  \"description\": \"Template Instagram Story untuk promo skincare\",\n  \"type\": \"image\",\n  \"file_url\": \"https://example.com/marketing-kit/banner-ig.jpg\",\n  \"file_size\": 1024000,\n  \"dimensions\": \"1080x1920\",\n  \"usage_guide\": \"Gunakan untuk Instagram Story dengan caption yang menarik\"\n}"}, "url": {"raw": "{{base_url}}/vendor/promo/1/marketing-kit", "host": ["{{base_url}}"], "path": ["vendor", "promo", "1", "marketing-kit"]}, "description": "Upload marketing kit untuk promo tertentu"}}, {"name": "List Marketing Kit", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vendor/promo/1/marketing-kit", "host": ["{{base_url}}"], "path": ["vendor", "promo", "1", "marketing-kit"]}, "description": "List marketing kit untuk promo tertentu"}}, {"name": "Delete Marketing Kit", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vendor/marketing-kit/1", "host": ["{{base_url}}"], "path": ["vendor", "marketing-kit", "1"]}, "description": "Hapus marketing kit tertentu"}}]}]}