{"info": {"name": "System API - Kbuy.me", "description": "Collection untuk System Internal - automation, monitoring, cron jobs", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "apikey", "apikey": [{"key": "key", "value": "X-Internal-Token", "type": "string"}, {"key": "value", "value": "{{internal_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "internal_token", "value": "your_internal_api_key_here", "type": "string"}], "item": [{"name": "🤖 Campaign Automation", "item": [{"name": "Check Campaign Budgets", "request": {"method": "POST", "header": [{"key": "X-Internal-Token", "value": "{{internal_token}}"}], "url": {"raw": "{{base_url}}/system/check-campaign-budgets", "host": ["{{base_url}}"], "path": ["system", "check-campaign-budgets"]}, "description": "<PERSON>ron job untuk cek budget dan auto-stop campaign yang habis"}}, {"name": "Check Campaign Dates", "request": {"method": "POST", "header": [{"key": "X-Internal-Token", "value": "{{internal_token}}"}], "url": {"raw": "{{base_url}}/system/check-campaign-dates", "host": ["{{base_url}}"], "path": ["system", "check-campaign-dates"]}, "description": "<PERSON><PERSON> job untuk auto-stop campaign yang sudah end_date"}}, {"name": "Campaigns Expiring Soon", "request": {"method": "GET", "header": [{"key": "X-Internal-Token", "value": "{{internal_token}}"}], "url": {"raw": "{{base_url}}/system/campaigns/expiring-soon", "host": ["{{base_url}}"], "path": ["system", "campaigns", "expiring-soon"]}, "description": "List campaign yang akan habis budget/tanggal dalam 24-48 jam"}}, {"name": "Auto Stop Campaign", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Internal-Token", "value": "{{internal_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"budget_exhausted\"\n}"}, "url": {"raw": "{{base_url}}/system/campaign/1/auto-stop", "host": ["{{base_url}}"], "path": ["system", "campaign", "1", "auto-stop"]}, "description": "Auto-stop campaign den<PERSON> alasan (budget_exhausted/date_ended)"}}]}, {"name": "📧 Notifications", "item": [{"name": "Send Campaign Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Internal-Token", "value": "{{internal_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"campaign_id\": 123,\n  \"vendor_id\": 1,\n  \"notification_type\": \"new_campaign\"\n}"}, "url": {"raw": "{{base_url}}/system/send-campaign-notification", "host": ["{{base_url}}"], "path": ["system", "send-campaign-notification"]}, "description": "Internal API untuk trigger notifikasi ke followers"}}]}, {"name": "📊 Stats Management", "item": [{"name": "Update Vendor Stats", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Internal-Token", "value": "{{internal_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"total_kol_partners\": 150,\n  \"total_campaigns\": 25,\n  \"total_commission_paid\": 5000000\n}"}, "url": {"raw": "{{base_url}}/vendor/1/stats/update", "host": ["{{base_url}}"], "path": ["vendor", "1", "stats", "update"]}, "description": "Update statistik vendor (internal use)"}}, {"name": "Recalculate Vendor Stats", "request": {"method": "PUT", "header": [{"key": "X-Internal-Token", "value": "{{internal_token}}"}], "url": {"raw": "{{base_url}}/vendor/1/stats/recalculate", "host": ["{{base_url}}"], "path": ["vendor", "1", "stats", "recalculate"]}, "description": "Recalculate semua statistik vendor (maintenance)"}}]}]}