<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Vendor\VendorController;
use App\Http\Controllers\Vendor\PromoController;
use App\Http\Controllers\Vendor\MarketingKitController;
use App\Http\Controllers\Vendor\StatsController;
use App\Http\Controllers\Vendor\FollowController;
use App\Http\Controllers\Vendor\ReviewController;
use App\Http\Controllers\Public\VendorPublicController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\FeedController;
use App\Http\Controllers\PerformanceController;

// Auth & Verifikasi
Route::post('/register', [AuthController::class, 'register']);
Route::post('/request-otp', [AuthController::class, 'requestOtp']);
Route::post('/verify-otp', [AuthController::class, 'verifyOtp']);
Route::post('/email/verify', [AuthController::class, 'verifyEmail']);
Route::post('/email/resend', [AuthController::class, 'resendEmail']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');
Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);

// User Management
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', [UserController::class, 'profile']);
    Route::put('/user', [UserController::class, 'update']);
    Route::delete('/user', [UserController::class, 'destroy']);
    Route::put('/user/payment', [UserController::class, 'updatePayment']);
    Route::put('/user/social-media', [UserController::class, 'updateSocialMedia']);
});

// Route group untuk vendor (autentikasi vendor)
Route::prefix('vendor')->middleware('auth:sanctum')->group(function () {
    Route::post('register', [VendorController::class, 'register'])->withoutMiddleware(['auth:sanctum']);
    Route::get('check-id', [VendorController::class, 'checkId'])->withoutMiddleware(['auth:sanctum']);
    Route::get('/', [VendorController::class, 'profile']);
    Route::put('/', [VendorController::class, 'update']);
    Route::delete('/', [VendorController::class, 'delete']);

    // Promo
    Route::post('promo', [PromoController::class, 'create']);
    Route::get('promo', [PromoController::class, 'index']);
    Route::get('promo/{id}', [PromoController::class, 'show']);
    Route::put('promo/{id}', [PromoController::class, 'update']);
    Route::delete('promo/{id}', [PromoController::class, 'delete']);
    Route::put('promo/{id}/pause', [PromoController::class, 'pause']);
    Route::put('promo/{id}/extend', [PromoController::class, 'extend']);
    Route::get('promo/{id}/budget-tracking', [PromoController::class, 'budgetTracking']);

    // Marketing Kit
    Route::post('promo/{promo_id}/marketing-kit', [MarketingKitController::class, 'create']);
    Route::get('promo/{promo_id}/marketing-kit', [MarketingKitController::class, 'index']);
    Route::delete('marketing-kit/{id}', [MarketingKitController::class, 'delete']);

    // Statistik
    Route::put('{id}/stats/update', [StatsController::class, 'update'])->middleware('internal');
    Route::post('{id}/rating', [VendorController::class, 'rate']);
    Route::put('{id}/stats/recalculate', [StatsController::class, 'recalculate'])->middleware('internal');
});

// Endpoint publik (untuk KOL dan umum)
Route::prefix('public')->group(function () {
    Route::get('vendors', [VendorPublicController::class, 'index']);
    Route::get('vendor/{id}', [VendorPublicController::class, 'show']);
    Route::get('vendor/{id}/promos', [VendorPublicController::class, 'promos']);
    Route::get('vendors/search', [VendorPublicController::class, 'search']);
    Route::get('vendors/recommended', [VendorPublicController::class, 'recommended']);
    Route::get('vendors/trending', [VendorPublicController::class, 'trending']);
    Route::get('vendors/new', [VendorPublicController::class, 'new']);

    // Follow & Review
    Route::post('vendor/{id}/follow', [FollowController::class, 'follow'])->middleware('auth:sanctum');
    Route::delete('vendor/{id}/follow', [FollowController::class, 'unfollow'])->middleware('auth:sanctum');
    Route::get('kol/{kol_id}/followed-vendors', [FollowController::class, 'listFollowed'])->middleware('auth:sanctum');
    Route::post('vendor/{id}/review', [ReviewController::class, 'create'])->middleware('auth:sanctum');
    Route::get('vendor/{id}/reviews', [ReviewController::class, 'index']);
    Route::put('vendor/{id}/review/{review_id}', [ReviewController::class, 'update'])->middleware('auth:sanctum');
    Route::delete('vendor/{id}/review/{review_id}', [ReviewController::class, 'delete'])->middleware('auth:sanctum');
});

// Notifikasi & Feed (KOL)
Route::prefix('kol')->middleware('auth:sanctum')->group(function () {
    Route::get('notifications', [NotificationController::class, 'index']);
    Route::put('notifications/{id}/read', [NotificationController::class, 'markRead']);
    Route::put('notifications/mark-all-read', [NotificationController::class, 'markAllRead']);
    Route::delete('notifications/{id}', [NotificationController::class, 'delete']);
    Route::get('feed', [FeedController::class, 'index']);
    Route::put('feed/{id}/seen', [FeedController::class, 'markSeen']);
    Route::get('feed/unread-count', [FeedController::class, 'unreadCount']);
});

// Performance & Automation
Route::prefix('system')->middleware('internal')->group(function () {
    Route::post('send-campaign-notification', [NotificationController::class, 'sendCampaignNotification']);
    Route::post('check-campaign-budgets', [PromoController::class, 'checkCampaignBudgets']);
    Route::post('check-campaign-dates', [PromoController::class, 'checkCampaignDates']);
    Route::get('campaigns/expiring-soon', [PromoController::class, 'expiringSoon']);
    Route::put('campaign/{id}/auto-stop', [PromoController::class, 'autoStop']);
});

// Dashboard vendor
Route::get('vendor/dashboard/alerts', [VendorController::class, 'dashboardAlerts'])->middleware('auth:sanctum');

// Performance tracking publik
Route::get('public/vendor/{id}/live-performance', [PerformanceController::class, 'livePerformance']);
Route::get('public/promo/{id}/analytics', [PerformanceController::class, 'promoAnalytics']);
Route::get('public/vendor/{id}/performance-history', [PerformanceController::class, 'performanceHistory']);
Route::get('public/campaign-leaderboard', [PerformanceController::class, 'campaignLeaderboard']); 