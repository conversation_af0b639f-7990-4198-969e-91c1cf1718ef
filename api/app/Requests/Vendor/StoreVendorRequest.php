<?php

namespace App\Http\Requests\Vendor;

use Illuminate\Foundation\Http\FormRequest;

class StoreVendorRequest extends FormRequest
{
    // Otorisasi request
    public function authorize()
    {
        return true;
    }

    // Aturan validasi
    public function rules()
    {
        return [
            'name' => 'required|string|max:150',
            'email' => 'required|email|unique:vendors,email',
            'phone_number' => 'required|string|max:20|unique:vendors,phone_number',
            'source' => 'required|in:gass,klikbuy',
        ];
    }
}
