<?php

namespace App\Http\Requests\Vendor;

use Illuminate\Foundation\Http\FormRequest;

class UpdateVendorRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'about' => 'nullable|string',
            'profile_pic_url' => 'nullable|url',
            'instagram_url' => 'nullable|url',
            'tiktok_url' => 'nullable|url',
            'website_url' => 'nullable|url',
            'address' => 'nullable|string|max:255',
            'brand_category' => 'nullable|string|max:50',
            'business_type' => 'nullable|string|max:50',
        ];
    }
}
