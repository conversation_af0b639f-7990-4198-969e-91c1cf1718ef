<?php

namespace App\Http\Requests\Promo;

use Illuminate\Foundation\Http\FormRequest;

class StorePromoRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'nama_promo' => 'required|string|max:200',
            'deskripsi' => 'nullable|string',
            'start_date' => 'required|date|after:today',
            'end_date' => 'required|date|after:start_date',
            'budget_limit' => 'required|numeric|min:50000',
            'commission_model' => 'required|in:single,multi',
            'target_type' => 'required|in:whatsapp,url',
            'target_value' => 'required|string',
            'max_closing' => 'nullable|integer|min:1',
            'komisi_type' => 'required|in:fixed,percentage',
            // Minimal 1 jenis komisi harus diisi
            'komisi_per_lead' => 'nullable|numeric|min:0',
            'komisi_per_mql' => 'nullable|numeric|min:0',
            'komisi_per_prospek' => 'nullable|numeric|min:0',
            'komisi_per_closing' => 'nullable|numeric|min:0',
        ];
    }
}
