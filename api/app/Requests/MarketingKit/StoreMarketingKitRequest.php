<?php

namespace App\Http\Requests\MarketingKit;

use Illuminate\Foundation\Http\FormRequest;

class StoreMarketingKitRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'nama_kit' => 'required|string|max:200',
            'tipe' => 'required|in:image,video,pdf,link',
            'url_file' => 'required|url',
            'file_size' => 'nullable|integer|min:0',
        ];
    }
}
