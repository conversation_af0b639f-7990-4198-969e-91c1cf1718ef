<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Promo extends Model
{
    // <PERSON>a tabel
    protected $table = 'promos';

    // <PERSON><PERSON>m yang bisa diisi
    protected $fillable = [
        'vendor_id', 'nama_promo', 'deskripsi', 'start_date', 'end_date', 'budget_limit',
        'current_budget_used', 'stopped_reason', 'target_type', 'target_value', 'max_closing',
        'commission_model', 'komisi_per_lead', 'komisi_per_mql', 'komisi_per_prospek',
        'komisi_per_closing', 'komisi_type', 'status'
    ];

    // Relasi ke vendor
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }

    // Relasi ke marketing kit
    public function marketingKits()
    {
        return $this->hasMany(MarketingKit::class, 'promo_id');
    }
}
