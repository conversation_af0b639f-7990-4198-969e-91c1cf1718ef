<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VendorStatistic extends Model
{
    // <PERSON>a tabel
    protected $table = 'vendor_statistics';

    // <PERSON><PERSON><PERSON> yang bisa diisi
    protected $fillable = [
        'vendor_id', 'total_commission_paid', 'commission_range_min', 'commission_range_max',
        'top_earner_this_month', 'top_earner_amount', 'campaign_budget_available',
        'total_campaign_active', 'total_clicks', 'total_closing', 'updated_at'
    ];

    // <PERSON>lasi ke vendor
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }
}
