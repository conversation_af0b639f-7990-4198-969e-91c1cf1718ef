<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VendorPerformanceDaily extends Model
{
    // Nama tabel
    protected $table = 'vendor_performance_daily';

    // Kolom yang bisa diisi
    protected $fillable = [
        'vendor_id', 'date', 'total_clicks', 'total_leads', 'total_prospects', 'total_closing',
        'commission_paid', 'active_kols', 'created_at'
    ];

    // Relasi ke vendor
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }
}
