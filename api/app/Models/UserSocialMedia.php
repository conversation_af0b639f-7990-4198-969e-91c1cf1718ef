<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserSocialMedia extends Model
{
    use HasFactory;

    protected $table = 'user_social_media';

    protected $fillable = [
        'user_id',
        'platform',
        'username_or_url',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
