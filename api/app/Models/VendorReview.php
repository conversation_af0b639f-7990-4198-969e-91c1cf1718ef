<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VendorReview extends Model
{
    // <PERSON>a tabel
    protected $table = 'vendor_reviews';

    // <PERSON><PERSON>m yang bisa diisi
    protected $fillable = [
        'vendor_id', 'kol_id', 'campaign_id', 'rating', 'review', 'is_verified', 'created_at', 'updated_at'
    ];

    // Relasi ke vendor
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }
}
