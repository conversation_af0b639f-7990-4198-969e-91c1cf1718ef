<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CampaignNotification extends Model
{
    // <PERSON>a tabel
    protected $table = 'campaign_notifications';

    // <PERSON><PERSON><PERSON> yang bisa diisi
    protected $fillable = [
        'promo_id', 'vendor_id', 'kol_id', 'type', 'title', 'message', 'is_read', 'created_at'
    ];

    // Relasi ke vendor
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }

    // Relasi ke promo
    public function promo()
    {
        return $this->belongsTo(Promo::class, 'promo_id');
    }
}
