<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    // <PERSON>a tabel
    protected $table = 'vendors';

    // <PERSON><PERSON>m yang bisa diisi
    protected $fillable = [
        'name', 'email', 'phone_number', 'status', 'source', 'about', 'profile_pic_url',
        'instagram_url', 'tiktok_url', 'website_url', 'address', 'is_verified',
        'brand_category', 'business_type', 'rating', 'total_kol_partners',
        'payment_reliability_score', 'avg_commission_amount'
    ];

    // Default values
    protected $attributes = [
        'status' => 1, // 1 = active
        'is_verified' => 0,
        'rating' => 0,
        'total_kol_partners' => 0,
        'payment_reliability_score' => 0,
        'avg_commission_amount' => 0
    ];

    // Relasi ke promo
    public function promos()
    {
        return $this->hasMany(Promo::class, 'vendor_id');
    }
}
