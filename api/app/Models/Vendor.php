<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    // <PERSON>a tabel
    protected $table = 'vendors';

    // <PERSON><PERSON>m yang bisa diisi
    protected $fillable = [
        'name', 'email', 'phone_number', 'status', 'source', 'about', 'profile_pic_url',
        'instagram_url', 'tiktok_url', 'website_url', 'address', 'is_verified',
        'brand_category', 'business_type', 'rating', 'total_kol_partners',
        'payment_reliability_score', 'avg_commission_amount'
    ];

    // Relasi ke promo
    public function promos()
    {
        return $this->hasMany(Promo::class, 'vendor_id');
    }
}
