<?php

namespace App\Http\Requests\Promo;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePromoRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'nama_promo' => 'nullable|string|max:200',
            'deskripsi' => 'nullable|string',
            'start_date' => 'nullable|date|after:today',
            'end_date' => 'nullable|date|after:start_date',
            'budget_limit' => 'nullable|numeric|min:50000',
            'commission_model' => 'nullable|in:single,multi',
            'target_type' => 'nullable|in:whatsapp,url',
            'target_value' => 'nullable|string',
            'max_closing' => 'nullable|integer|min:1',
            'komisi_type' => 'nullable|in:fixed,percentage',
            'komisi_per_lead' => 'nullable|numeric|min:0',
            'komisi_per_mql' => 'nullable|numeric|min:0',
            'komisi_per_prospek' => 'nullable|numeric|min:0',
            'komisi_per_closing' => 'nullable|numeric|min:0',
        ];
    }
}
