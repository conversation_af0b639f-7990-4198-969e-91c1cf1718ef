<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Vendor;
use App\Models\Promo;

class VendorPublicController extends Controller
{
    // List semua vendor/brand (untuk ditampilkan ke KOL)
    public function index(Request $request)
    {
        $query = Vendor::where('status', 1); // Hanya vendor aktif

        // Filter berdasarkan kategori
        if ($request->has('brand_category')) {
            $query->where('brand_category', $request->brand_category);
        }

        // Filter berdasarkan rating minimal
        if ($request->has('min_rating')) {
            $query->where('rating', '>=', $request->min_rating);
        }

        // Filter berdasarkan status verifikasi
        if ($request->has('is_verified')) {
            $query->where('is_verified', $request->is_verified);
        }

        // Sorting
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');
        
        if (in_array($sortBy, ['rating', 'total_kol_partners', 'avg_commission_amount', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $vendors = $query->paginate(15);

        return response()->json([
            'success' => true,
            'message' => 'Daftar vendor',
            'data' => $vendors
        ]);
    }

    // Detail lengkap vendor/brand untuk KOL
    public function show(Request $request, $id)
    {
        $vendor = Vendor::where('id', $id)->where('status', 1)->first();
        
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Vendor tidak ditemukan', 'data' => null], 404);
        }

        // Include promo aktif vendor
        $vendor->load(['promos' => function($query) {
            $query->where('status', 1)->orderBy('created_at', 'desc');
        }]);

        return response()->json([
            'success' => true,
            'message' => 'Detail vendor',
            'data' => $vendor
        ]);
    }

    // List promo aktif dari vendor tertentu
    public function promos(Request $request, $id)
    {
        $vendor = Vendor::where('id', $id)->where('status', 1)->first();
        
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Vendor tidak ditemukan', 'data' => null], 404);
        }

        $promos = Promo::where('vendor_id', $id)
            ->where('status', 1) // Hanya promo aktif
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'message' => 'Daftar promo vendor',
            'data' => $promos
        ]);
    }

    // Search vendor dengan multiple criteria
    public function search(Request $request)
    {
        $query = Vendor::where('status', 1);

        // Search berdasarkan keyword (nama atau deskripsi)
        if ($request->has('keyword')) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('about', 'like', "%{$keyword}%");
            });
        }

        // Filter kategori
        if ($request->has('category')) {
            $query->where('brand_category', $request->category);
        }

        // Filter range komisi
        if ($request->has('min_commission')) {
            $query->where('avg_commission_amount', '>=', $request->min_commission);
        }
        if ($request->has('max_commission')) {
            $query->where('avg_commission_amount', '<=', $request->max_commission);
        }

        // Filter rating minimal
        if ($request->has('min_rating')) {
            $query->where('rating', '>=', $request->min_rating);
        }

        // Filter lokasi
        if ($request->has('location')) {
            $query->where('address', 'like', "%{$request->location}%");
        }

        // Filter tipe bisnis
        if ($request->has('business_type')) {
            $query->where('business_type', $request->business_type);
        }

        // Filter verified
        if ($request->has('is_verified')) {
            $query->where('is_verified', $request->is_verified);
        }

        $vendors = $query->orderBy('rating', 'desc')->paginate(15);

        return response()->json([
            'success' => true,
            'message' => 'Hasil pencarian vendor',
            'data' => $vendors
        ]);
    }

    // Rekomendasi vendor untuk KOL (dummy logic)
    public function recommended(Request $request)
    {
        // Logic rekomendasi sederhana: vendor dengan rating tinggi dan komisi bagus
        $vendors = Vendor::where('status', 1)
            ->where('rating', '>=', 4.0)
            ->where('avg_commission_amount', '>=', 50000)
            ->orderBy('rating', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'message' => 'Rekomendasi vendor',
            'data' => $vendors
        ]);
    }

    // Vendor yang sedang trending (berdasarkan total KOL partners)
    public function trending(Request $request)
    {
        $vendors = Vendor::where('status', 1)
            ->orderBy('total_kol_partners', 'desc')
            ->orderBy('rating', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'message' => 'Vendor trending',
            'data' => $vendors
        ]);
    }

    // Vendor baru yang baru join platform
    public function new(Request $request)
    {
        $vendors = Vendor::where('status', 1)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'message' => 'Vendor baru',
            'data' => $vendors
        ]);
    }
}
