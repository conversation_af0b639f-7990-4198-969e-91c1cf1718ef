<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class FeedController extends Controller
{
    // Feed timeline campaign baru/update untuk KOL
    public function index(Request $request)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Daftar feed', 'data' => null]);
    }

    // Mark feed item sebagai sudah dilihat
    public function markSeen(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Feed ditandai sudah dilihat', 'data' => null]);
    }

    // Count notifikasi dan feed yang belum dibaca
    public function unreadCount(Request $request)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Jumlah feed/notifikasi belum dibaca', 'data' => null]);
    }
}
