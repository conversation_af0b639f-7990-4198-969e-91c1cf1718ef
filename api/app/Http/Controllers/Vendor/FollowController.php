<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\VendorFollow;
use App\Models\Vendor;
use Illuminate\Support\Facades\Auth;

class FollowController extends Controller
{
    // KOL follow vendor
    public function follow(Request $request, $id)
    {
        $kol = Auth::user();
        if (!$kol) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        // Cek apakah vendor exists dan aktif
        $vendor = Vendor::where('id', $id)->where('status', 1)->first();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Vendor tidak ditemukan', 'data' => null], 404);
        }

        // Cek apakah sudah follow sebelumnya
        $existingFollow = VendorFollow::where('vendor_id', $id)->where('kol_id', $kol->id)->first();
        if ($existingFollow) {
            return response()->json(['success' => false, 'message' => 'Sudah follow vendor ini', 'data' => null], 400);
        }

        // Buat record follow baru
        $follow = VendorFollow::create([
            'vendor_id' => $id,
            'kol_id' => $kol->id
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Vendor berhasil di-follow',
            'data' => $follow
        ]);
    }

    // KOL unfollow vendor
    public function unfollow(Request $request, $id)
    {
        $kol = Auth::user();
        if (!$kol) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        // Cari record follow
        $follow = VendorFollow::where('vendor_id', $id)->where('kol_id', $kol->id)->first();
        if (!$follow) {
            return response()->json(['success' => false, 'message' => 'Belum follow vendor ini', 'data' => null], 404);
        }

        $follow->delete();

        return response()->json([
            'success' => true,
            'message' => 'Vendor berhasil di-unfollow',
            'data' => null
        ]);
    }

    // List vendor yang di-follow KOL
    public function listFollowed(Request $request, $kol_id)
    {
        $kol = Auth::user();
        if (!$kol || $kol->id != $kol_id) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        $follows = VendorFollow::where('kol_id', $kol_id)
            ->with(['vendor' => function($query) {
                $query->where('status', 1)->select('id', 'name', 'profile_pic_url', 'brand_category', 'rating', 'avg_commission_amount');
            }])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'message' => 'Daftar vendor yang di-follow',
            'data' => $follows
        ]);
    }
}
