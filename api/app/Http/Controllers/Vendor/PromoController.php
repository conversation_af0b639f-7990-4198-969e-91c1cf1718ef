<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Promo;
use App\Http\Requests\Promo\StorePromoRequest;
use App\Http\Requests\Promo\UpdatePromoRequest;
use Illuminate\Support\Facades\Auth;

class PromoController extends Controller
{
    // Membuat promo baru
    public function create(StorePromoRequest $request)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }
        $data = $request->validated();
        $data['vendor_id'] = $vendor->id;
        $promo = Promo::create($data);
        return response()->json([
            'success' => true,
            'message' => 'Promo berhasil dibuat',
            'data' => $promo
        ]);
    }

    // List promo milik vendor
    public function index(Request $request)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }
        $query = Promo::where('vendor_id', $vendor->id);
        // Filter opsional
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }
        if ($request->has('start_date')) {
            $query->where('start_date', '>=', $request->start_date);
        }
        if ($request->has('budget_remaining')) {
            $query->whereRaw('(budget_limit - current_budget_used) >= ?', [$request->budget_remaining]);
        }
        $promos = $query->orderBy('created_at', 'desc')->get();
        return response()->json([
            'success' => true,
            'message' => 'Daftar promo',
            'data' => $promos
        ]);
    }

    // Detail promo
    public function show(Request $request, $id)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }
        $promo = Promo::where('vendor_id', $vendor->id)->where('id', $id)->first();
        if (!$promo) {
            return response()->json(['success' => false, 'message' => 'Promo tidak ditemukan', 'data' => null], 404);
        }
        return response()->json([
            'success' => true,
            'message' => 'Detail promo',
            'data' => $promo
        ]);
    }

    // Update promo
    public function update(UpdatePromoRequest $request, $id)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }
        $promo = Promo::where('vendor_id', $vendor->id)->where('id', $id)->first();
        if (!$promo) {
            return response()->json(['success' => false, 'message' => 'Promo tidak ditemukan', 'data' => null], 404);
        }
        $promo->update($request->validated());
        return response()->json([
            'success' => true,
            'message' => 'Promo berhasil diupdate',
            'data' => $promo
        ]);
    }

    // Hapus promo
    public function delete(Request $request, $id)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }
        $promo = Promo::where('vendor_id', $vendor->id)->where('id', $id)->first();
        if (!$promo) {
            return response()->json(['success' => false, 'message' => 'Promo tidak ditemukan', 'data' => null], 404);
        }
        $promo->delete();
        return response()->json([
            'success' => true,
            'message' => 'Promo berhasil dihapus',
            'data' => null
        ]);
    }

    // Pause/unpause campaign
    public function pause(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Status campaign berhasil diubah', 'data' => null]);
    }

    // Extend end_date atau tambah budget
    public function extend(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Campaign berhasil diperpanjang', 'data' => null]);
    }

    // Real-time budget usage
    public function budgetTracking(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Budget tracking', 'data' => null]);
    }

    // Cron: cek budget dan auto-stop campaign
    public function checkCampaignBudgets(Request $request)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Cek budget campaign selesai', 'data' => null]);
    }

    // Cron: auto-stop campaign yang sudah end_date
    public function checkCampaignDates(Request $request)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Cek tanggal campaign selesai', 'data' => null]);
    }

    // List campaign expiring soon
    public function expiringSoon(Request $request)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Daftar campaign yang akan habis', 'data' => null]);
    }

    // Auto-stop campaign
    public function autoStop(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Campaign berhasil di-auto-stop', 'data' => null]);
    }
}
