<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\MarketingKit;
use App\Models\Promo;
use App\Http\Requests\MarketingKit\StoreMarketingKitRequest;
use Illuminate\Support\Facades\Auth;

class MarketingKitController extends Controller
{
    // Tambah marketing kit ke promo
    public function create(StoreMarketingKitRequest $request, $promo_id)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        // Cek apakah promo milik vendor yang login
        $promo = Promo::where('vendor_id', $vendor->id)->where('id', $promo_id)->first();
        if (!$promo) {
            return response()->json(['success' => false, 'message' => 'Promo tidak ditemukan', 'data' => null], 404);
        }

        $data = $request->validated();
        $data['promo_id'] = $promo_id;
        $marketingKit = MarketingKit::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Marketing kit berhasil ditambahkan',
            'data' => $marketingKit
        ]);
    }

    // List marketing kit promo
    public function index(Request $request, $promo_id)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        // Cek apakah promo milik vendor yang login
        $promo = Promo::where('vendor_id', $vendor->id)->where('id', $promo_id)->first();
        if (!$promo) {
            return response()->json(['success' => false, 'message' => 'Promo tidak ditemukan', 'data' => null], 404);
        }

        $marketingKits = MarketingKit::where('promo_id', $promo_id)->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'message' => 'Daftar marketing kit',
            'data' => $marketingKits
        ]);
    }

    // Hapus marketing kit
    public function delete(Request $request, $id)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        // Cek apakah marketing kit milik vendor yang login (via promo)
        $marketingKit = MarketingKit::whereHas('promo', function($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })->where('id', $id)->first();

        if (!$marketingKit) {
            return response()->json(['success' => false, 'message' => 'Marketing kit tidak ditemukan', 'data' => null], 404);
        }

        $marketingKit->delete();

        return response()->json([
            'success' => true,
            'message' => 'Marketing kit berhasil dihapus',
            'data' => null
        ]);
    }
}
