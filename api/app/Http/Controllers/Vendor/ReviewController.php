<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\VendorReview;
use App\Models\Vendor;
use App\Models\Promo;
use App\Http\Requests\Vendor\RateVendorRequest;
use Illuminate\Support\Facades\Auth;

class ReviewController extends Controller
{
    // KOL kasih review ke vendor
    public function create(RateVendorRequest $request, $id)
    {
        $kol = Auth::user();
        if (!$kol) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        // Cek apakah vendor exists dan aktif
        $vendor = Vendor::where('id', $id)->where('status', 1)->first();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Vendor tidak ditemukan', 'data' => null], 404);
        }

        // Validasi campaign_id jika disertakan
        $campaignId = $request->campaign_id ?? null;
        if ($campaignId) {
            $campaign = Promo::where('id', $campaignId)->where('vendor_id', $id)->first();
            if (!$campaign) {
                return response()->json(['success' => false, 'message' => 'Campaign tidak ditemukan', 'data' => null], 404);
            }

            // Cek apakah sudah review campaign ini sebelumnya
            $existingReview = VendorReview::where('vendor_id', $id)
                ->where('kol_id', $kol->id)
                ->where('campaign_id', $campaignId)
                ->first();
            if ($existingReview) {
                return response()->json(['success' => false, 'message' => 'Sudah review campaign ini', 'data' => null], 400);
            }
        }

        $data = $request->validated();
        $data['vendor_id'] = $id;
        $data['kol_id'] = $kol->id;
        $data['campaign_id'] = $campaignId;

        $review = VendorReview::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Review berhasil ditambahkan',
            'data' => $review
        ]);
    }

    // List review dari KOL lain
    public function index(Request $request, $id)
    {
        $vendor = Vendor::where('id', $id)->where('status', 1)->first();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Vendor tidak ditemukan', 'data' => null], 404);
        }

        $query = VendorReview::where('vendor_id', $id);

        // Filter berdasarkan rating
        if ($request->has('rating')) {
            $query->where('rating', $request->rating);
        }

        // Filter berdasarkan verifikasi
        if ($request->has('is_verified')) {
            $query->where('is_verified', $request->is_verified);
        }

        $reviews = $query->with(['vendor:id,name', 'promo:id,nama_promo'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'message' => 'Daftar review',
            'data' => $reviews
        ]);
    }

    // Update review KOL
    public function update(RateVendorRequest $request, $id, $review_id)
    {
        $kol = Auth::user();
        if (!$kol) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        // Cari review milik KOL yang login
        $review = VendorReview::where('vendor_id', $id)
            ->where('kol_id', $kol->id)
            ->where('id', $review_id)
            ->first();

        if (!$review) {
            return response()->json(['success' => false, 'message' => 'Review tidak ditemukan', 'data' => null], 404);
        }

        $review->update($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Review berhasil diupdate',
            'data' => $review
        ]);
    }

    // Hapus review KOL
    public function delete(Request $request, $id, $review_id)
    {
        $kol = Auth::user();
        if (!$kol) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        // Cari review milik KOL yang login
        $review = VendorReview::where('vendor_id', $id)
            ->where('kol_id', $kol->id)
            ->where('id', $review_id)
            ->first();

        if (!$review) {
            return response()->json(['success' => false, 'message' => 'Review tidak ditemukan', 'data' => null], 404);
        }

        $review->delete();

        return response()->json([
            'success' => true,
            'message' => 'Review berhasil dihapus',
            'data' => null
        ]);
    }
}
