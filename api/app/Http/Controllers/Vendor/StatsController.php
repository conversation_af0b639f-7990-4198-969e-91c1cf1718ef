<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class StatsController extends Controller
{
    // Update statistik vendor (internal use)
    public function update(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Statistik vendor berhasil diupdate', 'data' => null]);
    }

    // Recalculate semua statistik vendor (maintenance)
    public function recalculate(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Statistik vendor berhasil direkalkulasi', 'data' => null]);
    }
}
