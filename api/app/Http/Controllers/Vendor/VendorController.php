<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Vendor;
use App\Http\Requests\Vendor\StoreVendorRequest;
use App\Http\Requests\Vendor\UpdateVendorRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class VendorController extends Controller
{
    // Registrasi vendor baru (server-to-server)
    public function register(StoreVendorRequest $request)
    {
        // Validasi otomatis oleh StoreVendorRequest
        $data = $request->validated();
        $vendor = Vendor::create($data);
        return response()->json([
            'success' => true,
            'message' => 'Vendor berhasil didaftarkan',
            'data' => $vendor
        ]);
    }

    // Cek apakah ID/email/phone sudah terdaftar
    public function checkId(Request $request)
    {
        $email = $request->query('email');
        $phone = $request->query('phone_number');
        $id = $request->query('id');
        $exists = false;
        if ($email) {
            $exists = Vendor::where('email', $email)->exists();
        } elseif ($phone) {
            $exists = Vendor::where('phone_number', $phone)->exists();
        } elseif ($id) {
            $exists = Vendor::where('id', $id)->exists();
        }
        return response()->json([
            'success' => true,
            'message' => $exists ? 'Sudah terdaftar' : 'Belum terdaftar',
            'data' => ['exists' => $exists]
        ]);
    }

    // Get profile vendor (autentikasi)
    public function profile(Request $request)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }
        return response()->json([
            'success' => true,
            'message' => 'Profil vendor',
            'data' => $vendor
        ]);
    }

    // Update profile vendor
    public function update(UpdateVendorRequest $request)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }
        $data = $request->validated();
        $vendor->update($data);
        return response()->json([
            'success' => true,
            'message' => 'Profil vendor berhasil diupdate',
            'data' => $vendor
        ]);
    }

    // Hapus akun vendor
    public function delete(Request $request)
    {
        $vendor = Auth::user();
        if (!$vendor) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }
        $vendor->delete();
        return response()->json([
            'success' => true,
            'message' => 'Akun vendor berhasil dihapus',
            'data' => null
        ]);
    }

    // KOL kasih rating ke vendor (dummy, detail di ReviewController)
    public function rate(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Rating berhasil diberikan', 'data' => null]);
    }

    // Dashboard alerts untuk vendor (dummy)
    public function dashboardAlerts(Request $request)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Dashboard alerts', 'data' => null]);
    }
}
