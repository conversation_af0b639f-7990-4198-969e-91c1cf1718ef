<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class PerformanceController extends Controller
{
    // Live performance data vendor hari ini
    public function livePerformance(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Live performance vendor', 'data' => null]);
    }

    // Detail analytics promo tertentu
    public function promoAnalytics(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Analytics promo', 'data' => null]);
    }

    // History performa vendor per bulan
    public function performanceHistory(Request $request, $id)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'History performa vendor', 'data' => null]);
    }

    // Leaderboard campaign/vendor berdasarkan performa
    public function campaignLeaderboard(Request $request)
    {
        // ...
        return response()->json(['success' => true, 'message' => 'Leaderboard campaign/vendor', 'data' => null]);
    }
}
