<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    //
    public function profile(Request $request) {
        $user = DB::table('users')->where('id', $request->user()->id)->first();

        // Get social media data
        $socialMedia = DB::table('user_social_media')->where('user_id', $request->user()->id)->get();

        // Convert user to array and add social media
        $userData = (array) $user;
        $userData['social_media'] = $socialMedia->toArray();

        // Parse tujuan_pembayaran JSON if exists
        if ($userData['tujuan_pembayaran']) {
            $userData['tujuan_pembayaran'] = json_decode($userData['tujuan_pembayaran'], true);
        }

        return response()->json(['success'=>true, 'data'=>$userData]);
    }

    public function update(Request $request) {
        $userId = $request->user()->id;
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:100',
            'email' => 'sometimes|email|max:150|unique:users,email,'.$userId,
            'phone_number' => 'sometimes|string|max:20|unique:users,phone_number,'.$userId,
            'city' => 'sometimes|string|max:100',
            'country' => 'sometimes|string|max:100',
        ]);
        if ($validator->fails()) {
            return response()->json(['success'=>false, 'message'=>'Validasi gagal', 'errors'=>$validator->errors()], 422);
        }
        $data = $validator->validated();
        $data['updated_at'] = now();
        DB::table('users')->where('id', $userId)->update($data);
        $user = DB::table('users')->where('id', $userId)->first();
        return response()->json(['success'=>true, 'message'=>'Update profil berhasil', 'data'=>$user]);
    }

    public function destroy(Request $request) {
        $userId = $request->user()->id;
        DB::table('users')->where('id', $userId)->delete();
        return response()->json(['success'=>true, 'message'=>'Akun berhasil dihapus']);
    }

    public function updatePayment(Request $request) {
        $userId = $request->user()->id;
        $validator = Validator::make($request->all(), [
            'tujuan_pembayaran' => 'required|array',
        ]);
        if ($validator->fails()) {
            return response()->json(['success'=>false, 'message'=>'Validasi gagal', 'errors'=>$validator->errors()], 422);
        }
        DB::table('users')->where('id', $userId)->update([
            'tujuan_pembayaran' => json_encode($request->tujuan_pembayaran),
            'updated_at' => now(),
        ]);
        return response()->json(['success'=>true, 'message'=>'Tujuan pembayaran berhasil diupdate']);
    }

    public function updateSocialMedia(Request $request) {
        $userId = $request->user()->id;
        $validator = Validator::make($request->all(), [
            'social_media' => 'required|array',
            'social_media.*.platform' => 'required|string',
            'social_media.*.username_or_url' => 'required|string',
        ]);
        if ($validator->fails()) {
            return response()->json(['success'=>false, 'message'=>'Validasi gagal', 'errors'=>$validator->errors()], 422);
        }
        DB::table('user_social_media')->where('user_id', $userId)->delete();
        foreach ($request->social_media as $sm) {
            DB::table('user_social_media')->insert([
                'user_id' => $userId,
                'platform' => $sm['platform'],
                'username_or_url' => $sm['username_or_url'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        return response()->json(['success'=>true, 'message'=>'Social media berhasil diupdate']);
    }
}
