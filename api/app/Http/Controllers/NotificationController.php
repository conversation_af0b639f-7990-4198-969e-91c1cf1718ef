<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CampaignNotification;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    // List notifikasi untuk KOL
    public function index(Request $request)
    {
        $kol = Auth::user();
        if (!$kol) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        $query = CampaignNotification::where('kol_id', $kol->id);

        // Filter berdasarkan status baca
        if ($request->has('is_read')) {
            $query->where('is_read', $request->is_read);
        }

        // Filter berdasarkan tipe notifikasi
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter berdasarkan rentang tanggal
        if ($request->has('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }
        if ($request->has('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        $notifications = $query->with(['vendor:id,name,profile_pic_url', 'promo:id,nama_promo'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'message' => 'Daftar notifikasi',
            'data' => $notifications
        ]);
    }

    // Mark notifikasi sebagai sudah dibaca
    public function markRead(Request $request, $id)
    {
        $kol = Auth::user();
        if (!$kol) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        $notification = CampaignNotification::where('kol_id', $kol->id)->where('id', $id)->first();
        if (!$notification) {
            return response()->json(['success' => false, 'message' => 'Notifikasi tidak ditemukan', 'data' => null], 404);
        }

        $notification->update(['is_read' => 1]);

        return response()->json([
            'success' => true,
            'message' => 'Notifikasi ditandai sudah dibaca',
            'data' => $notification
        ]);
    }

    // Mark semua notifikasi sebagai sudah dibaca
    public function markAllRead(Request $request)
    {
        $kol = Auth::user();
        if (!$kol) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        $updated = CampaignNotification::where('kol_id', $kol->id)
            ->where('is_read', 0)
            ->update(['is_read' => 1]);

        return response()->json([
            'success' => true,
            'message' => 'Semua notifikasi ditandai sudah dibaca',
            'data' => ['updated_count' => $updated]
        ]);
    }

    // Hapus notifikasi tertentu
    public function delete(Request $request, $id)
    {
        $kol = Auth::user();
        if (!$kol) {
            return response()->json(['success' => false, 'message' => 'Unauthorized', 'data' => null], 401);
        }

        $notification = CampaignNotification::where('kol_id', $kol->id)->where('id', $id)->first();
        if (!$notification) {
            return response()->json(['success' => false, 'message' => 'Notifikasi tidak ditemukan', 'data' => null], 404);
        }

        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notifikasi berhasil dihapus',
            'data' => null
        ]);
    }

    // Internal API untuk trigger notifikasi ke followers (dummy implementation)
    public function sendCampaignNotification(Request $request)
    {
        // Logic untuk mengirim notifikasi ke semua followers vendor
        // saat vendor membuat campaign baru
        // ...
        return response()->json(['success' => true, 'message' => 'Notifikasi campaign berhasil dikirim', 'data' => null]);
    }
}
