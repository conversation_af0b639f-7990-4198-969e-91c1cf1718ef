<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Services\WhatsappGateway;

class AuthController extends Controller
{
    public function register(Request $request) {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'email' => 'nullable|email|max:150|unique:users,email',
            'phone_number' => 'nullable|string|max:20|unique:users,phone_number',
            'password' => 'required|string|min:8',
            'city' => 'required|string|max:100',
            'country' => 'required|string|max:100',
        ]);
        if ($validator->fails()) {
            return response()->json(['success'=>false, 'message'=>'Validasi gagal', 'errors'=>$validator->errors()], 422);
        }
        $data = $validator->validated();
        $data['password'] = Hash::make($data['password']);
        $data['status'] = 1;
        $data['is_email_verified'] = false;
        $data['is_phone_verified'] = false;
        $data['created_at'] = now();
        $data['updated_at'] = now();
        $userId = DB::table('users')->insertGetId($data);
        $user = DB::table('users')->where('id', $userId)->first();
        // Kirim email verifikasi jika ada email
        if (!empty($user->email)) {
            $token = Str::random(64);
            DB::table('user_email_verifications')->insert([
                'user_id' => $userId,
                'token' => $token,
                'expired_at' => now()->addHours(24),
                'created_at' => now(),
            ]);
            $subject = 'Verifikasi Email Akun Anda';
            $link = url('/api/email/verify?token='.$token);
            $message = "Klik link berikut untuk verifikasi email Anda: $link";
            @mail($user->email, $subject, $message);
        }
        return response()->json(['success'=>true, 'message'=>'Registrasi berhasil', 'data'=>$user]);
    }

    public function requestOtp(Request $request) {
        $validator = Validator::make($request->all(), [
            'phone_number' => 'required|string|max:20',
            'type' => 'required|in:register,login,reset_password',
        ]);
        if ($validator->fails()) {
            return response()->json(['success'=>false, 'message'=>'Validasi gagal', 'errors'=>$validator->errors()], 422);
        }
        $data = $validator->validated();
        $user = DB::table('users')->where('phone_number', $data['phone_number'])->first();
        if (!$user && $data['type'] !== 'register') {
            return response()->json(['success'=>false, 'message'=>'User tidak ditemukan'], 404);
        }
        $otp = rand(100000, 999999);
        DB::table('user_otps')->insert([
            'user_id' => $user ? $user->id : null,
            'phone_number' => $data['phone_number'],
            'otp_code' => $otp,
            'type' => $data['type'],
            'expired_at' => now()->addMinutes(10),
            'created_at' => now(),
        ]);
        $wa = new WhatsappGateway();
        $wa->send(env('WA_SENDER', '628113608550'), $data['phone_number'], "Kode OTP Anda: $otp");
        return response()->json(['success'=>true, 'message'=>'OTP berhasil dikirim']);
    }

    public function verifyOtp(Request $request) {
        $validator = Validator::make($request->all(), [
            'phone_number' => 'required|string|max:20',
            'otp_code' => 'required|string|max:10',
            'type' => 'required|in:register,login,reset_password',
        ]);
        if ($validator->fails()) {
            return response()->json(['success'=>false, 'message'=>'Validasi gagal', 'errors'=>$validator->errors()], 422);
        }
        $data = $validator->validated();
        $otp = DB::table('user_otps')
            ->where('phone_number', $data['phone_number'])
            ->where('otp_code', $data['otp_code'])
            ->where('type', $data['type'])
            ->whereNull('used_at')
            ->where('expired_at', '>', now())
            ->first();
        if (!$otp) {
            return response()->json(['success'=>false, 'message'=>'OTP tidak valid atau sudah kadaluarsa'], 400);
        }
        DB::table('user_otps')->where('id', $otp->id)->update(['used_at' => now()]);
        if ($otp->user_id) {
            DB::table('users')->where('id', $otp->user_id)->update(['is_phone_verified' => true]);
        }
        return response()->json(['success'=>true, 'message'=>'OTP berhasil diverifikasi']);
    }

    public function verifyEmail(Request $request) {
        $token = $request->query('token') ?? $request->input('token');
        if (!$token) {
            return response()->json(['success'=>false, 'message'=>'Token tidak ditemukan'], 400);
        }
        $row = DB::table('user_email_verifications')
            ->where('token', $token)
            ->whereNull('used_at')
            ->where('expired_at', '>', now())
            ->first();
        if (!$row) {
            return response()->json(['success'=>false, 'message'=>'Token tidak valid atau sudah kadaluarsa'], 400);
        }
        DB::table('user_email_verifications')->where('id', $row->id)->update(['used_at' => now()]);
        DB::table('users')->where('id', $row->user_id)->update(['is_email_verified' => true]);
        return response()->json(['success'=>true, 'message'=>'Email berhasil diverifikasi']);
    }

    public function resendEmail(Request $request) {
        $user = $request->user();
        if (!$user || empty($user->email)) {
            return response()->json(['success'=>false, 'message'=>'User tidak ditemukan atau email kosong'], 404);
        }
        $token = Str::random(64);
        DB::table('user_email_verifications')->insert([
            'user_id' => $user->id,
            'token' => $token,
            'expired_at' => now()->addHours(24),
            'created_at' => now(),
        ]);
        $subject = 'Verifikasi Email Akun Anda';
        $link = url('/api/email/verify?token='.$token);
        $message = "Klik link berikut untuk verifikasi email Anda: $link";
        @mail($user->email, $subject, $message);
        return response()->json(['success'=>true, 'message'=>'Link verifikasi email sudah dikirim ulang']);
    }

    public function login(Request $request) {
        $validator = Validator::make($request->all(), [
            'email' => 'nullable|email',
            'phone_number' => 'nullable|string',
            'password' => 'required|string',
        ]);
        if ($validator->fails()) {
            return response()->json(['success'=>false, 'message'=>'Validasi gagal', 'errors'=>$validator->errors()], 422);
        }
        $data = $validator->validated();
        $user = DB::table('users')
            ->when(isset($data['email']), fn($q)=>$q->where('email', $data['email']))
            ->when(isset($data['phone_number']), fn($q)=>$q->orWhere('phone_number', $data['phone_number']))
            ->first();
        if (!$user || !Hash::check($data['password'], $user->password)) {
            return response()->json(['success'=>false, 'message'=>'Login gagal, data tidak cocok'], 401);
        }
        // Create Sanctum token
        $userModel = \App\Models\User::find($user->id);
        $token = $userModel->createToken('auth-token')->plainTextToken;
        return response()->json(['success'=>true, 'message'=>'Login berhasil', 'token'=>$token, 'data'=>$user]);
    }

    public function logout(Request $request) {
        $user = $request->user();
        if ($user) {
            $user->currentAccessToken()->delete();
        }
        return response()->json(['success'=>true, 'message'=>'Logout berhasil']);
    }

    public function forgotPassword(Request $request) {
        $validator = Validator::make($request->all(), [
            'email' => 'nullable|email',
            'phone_number' => 'nullable|string|max:20',
        ]);
        if ($validator->fails() || (!$request->email && !$request->phone_number)) {
            return response()->json(['success'=>false, 'message'=>'Email atau phone_number wajib diisi'], 422);
        }
        $user = DB::table('users')
            ->when($request->email, fn($q)=>$q->where('email', $request->email))
            ->when($request->phone_number, fn($q)=>$q->orWhere('phone_number', $request->phone_number))
            ->first();
        if (!$user) {
            return response()->json(['success'=>false, 'message'=>'User tidak ditemukan'], 404);
        }
        $token = Str::random(64);
        DB::table('password_resets')->insert([
            'user_id' => $user->id,
            'email' => $user->email,
            'phone_number' => $user->phone_number,
            'token' => $token,
            'expired_at' => now()->addHours(2),
            'created_at' => now(),
        ]);
        if ($user->email) {
            $subject = 'Reset Password Akun Anda';
            $link = url('/api/reset-password?token='.$token);
            $message = "Klik link berikut untuk reset password Anda: $link";
            @mail($user->email, $subject, $message);
        } else if ($user->phone_number) {
            $wa = new WhatsappGateway();
            $wa->send(env('WA_SENDER', '628113608550'), $user->phone_number, "Link reset password: ".url('/api/reset-password?token='.$token));
        }
        return response()->json(['success'=>true, 'message'=>'Link reset password sudah dikirim']);
    }

    public function resetPassword(Request $request) {
        $validator = Validator::make($request->all(), [
            'token' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);
        if ($validator->fails()) {
            return response()->json(['success'=>false, 'message'=>'Validasi gagal', 'errors'=>$validator->errors()], 422);
        }
        $row = DB::table('password_resets')
            ->where('token', $request->token)
            ->whereNull('used_at')
            ->where('expired_at', '>', now())
            ->first();
        if (!$row) {
            return response()->json(['success'=>false, 'message'=>'Token tidak valid atau sudah kadaluarsa'], 400);
        }
        DB::table('users')->where('id', $row->user_id)->update([
            'password' => Hash::make($request->password),
            'updated_at' => now(),
        ]);
        DB::table('password_resets')->where('id', $row->id)->update(['used_at' => now()]);
        return response()->json(['success'=>true, 'message'=>'Password berhasil direset']);
    }
}
