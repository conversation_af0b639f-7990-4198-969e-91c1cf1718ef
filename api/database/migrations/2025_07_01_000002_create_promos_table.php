<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Jalankan migrasi.
     */
    public function up(): void
    {
        Schema::create('promos', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('vendor_id')->index();
            $table->string('nama_promo', 200)->index();
            $table->text('deskripsi')->nullable();
            $table->timestamp('start_date')->index();
            $table->timestamp('end_date')->index();
            $table->decimal('budget_limit', 15, 2)->index();
            $table->decimal('current_budget_used', 15, 2)->default(0)->index();
            $table->enum('stopped_reason', ['manual','budget_exhausted','date_ended'])->nullable()->index();
            $table->enum('target_type', ['whatsapp','url'])->index();
            $table->string('target_value', 500);
            $table->unsignedInteger('max_closing')->nullable();
            $table->enum('commission_model', ['single','multi'])->index();
            $table->decimal('komisi_per_lead', 15, 2)->nullable();
            $table->decimal('komisi_per_mql', 15, 2)->nullable();
            $table->decimal('komisi_per_prospek', 15, 2)->nullable();
            $table->decimal('komisi_per_closing', 15, 2)->nullable();
            $table->enum('komisi_type', ['fixed','percentage'])->index();
            $table->tinyInteger('status')->unsigned()->index();
            $table->timestamp('created_at')->useCurrent()->index();
            $table->timestamp('updated_at')->nullable();
            $table->foreign('vendor_id')->references('id')->on('vendors')->onDelete('cascade');
        });
    }

    /**
     * Rollback migrasi.
     */
    public function down(): void
    {
        Schema::dropIfExists('promos');
    }
}; 