<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Jalankan migrasi.
     */
    public function up(): void
    {
        Schema::create('vendor_reviews', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('vendor_id')->index();
            $table->unsignedBigInteger('kol_id')->index();
            $table->unsignedBigInteger('campaign_id')->index();
            $table->tinyInteger('rating')->unsigned()->index();
            $table->text('review')->nullable();
            $table->tinyInteger('is_verified')->default(0)->index();
            $table->timestamp('created_at')->useCurrent()->index();
            $table->timestamp('updated_at')->nullable();
            
            // Foreign keys
            $table->foreign('vendor_id')->references('id')->on('vendors')->onDelete('cascade');
            $table->foreign('kol_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('campaign_id')->references('id')->on('promos')->onDelete('cascade');
            
            // Composite index untuk mencegah duplicate review per campaign
            $table->unique(['vendor_id', 'kol_id', 'campaign_id']);
        });
    }

    /**
     * Rollback migrasi.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_reviews');
    }
}; 