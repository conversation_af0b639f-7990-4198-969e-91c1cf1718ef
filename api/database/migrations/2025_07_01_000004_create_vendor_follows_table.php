<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Jalankan migrasi.
     */
    public function up(): void
    {
        Schema::create('vendor_follows', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('vendor_id')->index();
            $table->unsignedBigInteger('kol_id')->index();
            $table->timestamp('created_at')->useCurrent()->index();
            
            // Foreign keys
            $table->foreign('vendor_id')->references('id')->on('vendors')->onDelete('cascade');
            $table->foreign('kol_id')->references('id')->on('users')->onDelete('cascade');
            
            // Unique constraint untuk mencegah double follow
            $table->unique(['vendor_id', 'kol_id']);
        });
    }

    /**
     * Rollback migrasi.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_follows');
    }
}; 