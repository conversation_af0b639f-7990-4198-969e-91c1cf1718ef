<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Jalankan migrasi.
     */
    public function up(): void
    {
        Schema::create('vendors', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name', 150)->index();
            $table->string('email', 150)->unique();
            $table->string('phone_number', 20)->unique();
            $table->tinyInteger('status')->unsigned()->index();
            $table->enum('source', ['gass','klikbuy'])->index();
            $table->text('about')->nullable();
            $table->string('profile_pic_url', 255)->nullable();
            $table->string('instagram_url', 255)->nullable();
            $table->string('tiktok_url', 255)->nullable();
            $table->string('website_url', 255)->nullable();
            $table->string('address', 255)->nullable();
            $table->tinyInteger('is_verified')->default(0)->index();
            $table->string('brand_category', 50)->index();
            $table->string('business_type', 50)->index();
            $table->decimal('rating', 3, 2)->default(0)->index();
            $table->unsignedInteger('total_kol_partners')->default(0)->index();
            $table->tinyInteger('payment_reliability_score')->unsigned()->default(0)->index();
            $table->decimal('avg_commission_amount', 15, 2)->default(0)->index();
            $table->timestamp('created_at')->useCurrent()->index();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Rollback migrasi.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendors');
    }
}; 