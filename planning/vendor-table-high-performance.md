# Desain Tabel Database Vendor High Performance

## Tabel `vendors`

| Kolom                    | Tipe Data           | Index         | Keterangan                                    |
|--------------------------|--------------------|--------------|--------------------------------------------- |
| id                       | BIGINT UNSIGNED    | PRIMARY, AUTO_INCREMENT | Primary Key                                   |
| name                     | VARCHAR(150)       | INDEX        | Nama brand/vendor                             |
| email                    | VARCHAR(150)       | UNIQUE       | Email vendor                                  |
| phone_number             | VARCHAR(20)        | UNIQUE       | Nomor HP vendor                               |
| status                   | TINYINT UNSIGNED   | INDEX        | 0=inactive, 1=active, 2=suspended            |
| source                   | ENUM('gass','klikbuy') | INDEX    | Platform asal vendor                          |
| about                    | TEXT               |              | Deskripsi vendor/brand                        |
| profile_pic_url          | VARCHAR(255)       |              | URL foto/logo vendor                          |
| instagram_url            | VARCHAR(255)       |              | Link Instagram vendor                         |
| tiktok_url               | VARCHAR(255)       |              | Link TikTok vendor                            |
| website_url              | VARCHAR(255)       |              | Website resmi brand                           |
| address                  | VARCHAR(255)       |              | Alamat atau kota domisili vendor              |
| is_verified              | TINYINT(1)         | INDEX        | Status verifikasi: 0=belum, 1=sudah          |
| brand_category           | VARCHAR(50)        | INDEX        | Kategori brand (fashion, f&b, dll)           |
| business_type            | VARCHAR(50)        | INDEX        | Tipe bisnis (B2C, B2B, marketplace)          |
| rating                   | DECIMAL(3,2)       | INDEX        | Rating vendor 1-5 (misal: 4.50)              |
| total_kol_partners       | INT UNSIGNED       | INDEX        | Jumlah KOL yang pernah/sedang kerjasama       |
| payment_reliability_score| TINYINT UNSIGNED   | INDEX        | Skor keandalan pembayaran 1-100               |
| avg_commission_amount    | DECIMAL(15,2)      | INDEX        | Rata-rata nominal komisi                      |
| created_at               | TIMESTAMP          | INDEX        | Waktu dibuat                                  |
| updated_at               | TIMESTAMP          |              | Waktu update terakhir                         |

### Contoh Data
```sql
INSERT INTO vendors VALUES (
  1, 'Skincare ABC', '<EMAIL>', '***********', 1, 'klikbuy',
  'Brand skincare terpercaya dengan bahan alami', 'https://cdn.example.com/logo.jpg',
  'https://instagram.com/skincare_abc', 'https://tiktok.com/@skincare_abc',
  'https://skincare-abc.com', 'Jakarta', 1, 'kecantikan', 'B2C',
  4.75, 150, 95, 125000.00, NOW(), NOW()
);
```

### Catatan Optimasi
- Index pada kolom yang sering digunakan filter: brand_category, rating, is_verified, payment_reliability_score
- DECIMAL untuk rating dan commission agar presisi
- ENUM untuk source untuk efisiensi storage
- VARCHAR dengan panjang optimal untuk performa

---

## Tabel `promos`

| Kolom                | Tipe Data           | Index         | Keterangan                                    |
|----------------------|--------------------|--------------|--------------------------------------------- |
| id                   | BIGINT UNSIGNED    | PRIMARY, AUTO_INCREMENT | Primary Key                                   |
| vendor_id            | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke vendors.id                          |
| nama_promo           | VARCHAR(200)       | INDEX        | Nama promo/campaign                           |
| deskripsi            | TEXT               |              | Deskripsi promo                               |
| start_date           | TIMESTAMP          | INDEX        | Tanggal mulai campaign                        |
| end_date             | TIMESTAMP          | INDEX        | Tanggal selesai campaign                      |
| budget_limit         | DECIMAL(15,2)      | INDEX        | Batas maksimal budget campaign                |
| current_budget_used  | DECIMAL(15,2)      | INDEX        | Budget yang sudah terpakai                    |
| stopped_reason       | ENUM('manual','budget_exhausted','date_ended') NULL | INDEX | Alasan campaign stop (null jika masih aktif) |
| target_type          | ENUM('whatsapp','url') | INDEX    | Tipe redirect: WhatsApp atau URL              |
| target_value         | VARCHAR(500)       |              | Nomor WA atau URL tujuan                      |
| max_closing          | INT UNSIGNED       |              | Batas maksimal closing                        |
| commission_model     | ENUM('single','multi') | INDEX    | Model komisi: single atau multi-level        |
| komisi_per_lead      | DECIMAL(15,2)      |              | Komisi per lead/klik                          |
| komisi_per_mql       | DECIMAL(15,2)      |              | Komisi per Marketing Qualified Lead           |
| komisi_per_prospek   | DECIMAL(15,2)      |              | Komisi per prospek                            |
| komisi_per_closing   | DECIMAL(15,2)      |              | Komisi per closing                            |
| komisi_type          | ENUM('fixed','percentage') | INDEX | Tipe komisi: fixed atau percentage            |
| status               | TINYINT UNSIGNED   | INDEX        | 0=draft, 1=active, 2=paused, 3=ended, 4=budget_exhausted |
| created_at           | TIMESTAMP          | INDEX        | Waktu dibuat                                  |
| updated_at           | TIMESTAMP          |              | Waktu update terakhir                         |

### Catatan Optimasi
- Foreign key vendor_id dengan CASCADE DELETE
- Index pada vendor_id, commission_model, status untuk query cepat
- DECIMAL untuk komisi agar presisi finansial
- ENUM untuk efisiensi storage dan validasi data

---

## Tabel `marketing_kits`

| Kolom        | Tipe Data           | Index         | Keterangan                                    |
|--------------|--------------------|--------------|--------------------------------------------- |
| id           | BIGINT UNSIGNED    | PRIMARY, AUTO_INCREMENT | Primary Key                                   |
| promo_id     | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke promos.id                           |
| nama_kit     | VARCHAR(200)       |              | Nama marketing kit                            |
| tipe         | ENUM('image','video','pdf','link') | INDEX | Tipe marketing kit                            |
| url_file     | VARCHAR(500)       |              | URL file atau link                            |
| file_size    | INT UNSIGNED       |              | Ukuran file dalam bytes                       |
| created_at   | TIMESTAMP          | INDEX        | Waktu dibuat                                  |
| updated_at   | TIMESTAMP          |              | Waktu update terakhir                         |

### Catatan Optimasi
- Foreign key promo_id dengan CASCADE DELETE
- Index pada promo_id dan tipe untuk filter cepat
- file_size untuk tracking storage usage

---

## Tabel `vendor_statistics`

| Kolom                      | Tipe Data           | Index         | Keterangan                                    |
|----------------------------|--------------------|--------------|--------------------------------------------- |
| id                         | BIGINT UNSIGNED    | PRIMARY, AUTO_INCREMENT | Primary Key                                   |
| vendor_id                  | BIGINT UNSIGNED    | UNIQUE, FOREIGN KEY | Relasi ke vendors.id (one-to-one)             |
| total_commission_paid      | DECIMAL(15,2)      |              | Total komisi yang sudah dibayar               |
| commission_range_min       | DECIMAL(15,2)      |              | Komisi minimum                                |
| commission_range_max       | DECIMAL(15,2)      |              | Komisi maksimum                               |
| top_earner_this_month      | VARCHAR(100)       |              | KOL top earner bulan ini                      |
| top_earner_amount          | DECIMAL(15,2)      |              | Nominal earning top earner                    |
| campaign_budget_available  | DECIMAL(15,2)      |              | Dana tersedia di platform                    |
| total_campaign_active      | INT UNSIGNED       |              | Jumlah campaign aktif                         |
| total_clicks               | BIGINT UNSIGNED    |              | Total klik semua campaign                     |
| total_closing              | BIGINT UNSIGNED    |              | Total closing semua campaign                  |
| updated_at                 | TIMESTAMP          | INDEX        | Waktu update terakhir                         |

### Catatan Optimasi
- Tabel terpisah untuk statistik agar tabel vendor tetap ramping
- UNIQUE constraint pada vendor_id (one-to-one relationship)
- DECIMAL dengan presisi tinggi untuk rate dan ratio
- Index pada updated_at untuk tracking update

---

## Tabel `vendor_reviews`

| Kolom        | Tipe Data           | Index         | Keterangan                                    |
|--------------|--------------------|--------------|--------------------------------------------- |
| id           | BIGINT UNSIGNED    | PRIMARY, AUTO_INCREMENT | Primary Key                                   |
| vendor_id    | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke vendors.id                          |
| kol_id       | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke users.id (KOL)                      |
| campaign_id  | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke promos.id (campaign yang direview)  |
| rating       | TINYINT UNSIGNED   | INDEX        | Rating 1-5                                    |
| review       | TEXT               |              | Review text dari KOL                          |
| is_verified  | TINYINT(1)         | INDEX        | Review terverifikasi atau tidak               |
| created_at   | TIMESTAMP          | INDEX        | Waktu dibuat                                  |
| updated_at   | TIMESTAMP          |              | Waktu update terakhir                         |

### Catatan Optimasi
- Composite index pada (vendor_id, kol_id, campaign_id) untuk mencegah duplicate review per campaign
- Index pada rating untuk sorting dan filtering
- Foreign key ke vendor, KOL, dan campaign dengan CASCADE DELETE

---

## Tabel `vendor_follows`

| Kolom        | Tipe Data           | Index         | Keterangan                                    |
|--------------|--------------------|--------------|--------------------------------------------- |
| id           | BIGINT UNSIGNED    | PRIMARY, AUTO_INCREMENT | Primary Key                                   |
| vendor_id    | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke vendors.id                          |
| kol_id       | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke users.id (KOL)                      |
| created_at   | TIMESTAMP          | INDEX        | Waktu follow                                  |

### Catatan Optimasi
- Composite UNIQUE index pada (vendor_id, kol_id) mencegah double follow
- Index pada created_at untuk sorting timeline
- Tabel simple untuk performa optimal

---

## Tabel `vendor_performance_daily`

| Kolom               | Tipe Data           | Index         | Keterangan                                    |
|---------------------|--------------------|--------------|--------------------------------------------- |
| id                  | BIGINT UNSIGNED    | PRIMARY, AUTO_INCREMENT | Primary Key                                   |
| vendor_id           | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke vendors.id                          |
| date                | DATE               | INDEX        | Tanggal performance                           |
| total_clicks        | INT UNSIGNED       |              | Total klik hari ini                           |
| total_leads         | INT UNSIGNED       |              | Total leads hari ini                          |
| total_prospects     | INT UNSIGNED       |              | Total prospects hari ini                      |
| total_closing       | INT UNSIGNED       |              | Total closing hari ini                        |
| commission_paid     | DECIMAL(15,2)      |              | Komisi dibayar hari ini                       |
| active_kols         | INT UNSIGNED       |              | Jumlah KOL aktif hari ini                     |
| created_at          | TIMESTAMP          |              | Waktu record dibuat                           |

### Catatan Optimasi
- Composite UNIQUE index pada (vendor_id, date) mencegah duplicate
- Index pada date untuk query range tanggal
- Partitioning by date untuk performa query historical data
- Automated cleanup untuk data lama (retention policy)

---

## Tabel `campaign_notifications`

| Kolom        | Tipe Data           | Index         | Keterangan                                    |
|--------------|--------------------|--------------|--------------------------------------------- |
| id           | BIGINT UNSIGNED    | PRIMARY, AUTO_INCREMENT | Primary Key                                   |
| promo_id     | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke promos.id                           |
| vendor_id    | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke vendors.id                          |
| kol_id       | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke users.id (KOL)                      |
| type         | ENUM('new_campaign','campaign_update','campaign_ended') | INDEX | Tipe notifikasi                               |
| title        | VARCHAR(200)       |              | Judul notifikasi                              |
| message      | TEXT               |              | Isi pesan notifikasi                          |
| is_read      | TINYINT(1)         | INDEX        | Status baca: 0=belum, 1=sudah                 |
| created_at   | TIMESTAMP          | INDEX        | Waktu notifikasi dibuat                       |

### Catatan Optimasi
- Composite index pada (kol_id, is_read, created_at DESC) untuk query feed KOL yang optimal
- Composite index pada (vendor_id, promo_id, type) untuk query per campaign
- Foreign key dengan CASCADE DELETE untuk cleanup otomatis
- Partitioning by created_at untuk performa query historical data

---

## Tabel `kol_feed`

| Kolom        | Tipe Data           | Index         | Keterangan                                    |
|--------------|--------------------|--------------|--------------------------------------------- |
| id           | BIGINT UNSIGNED    | PRIMARY, AUTO_INCREMENT | Primary Key                                   |
| kol_id       | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke users.id (KOL)                      |
| vendor_id    | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke vendors.id                          |
| promo_id     | BIGINT UNSIGNED    | INDEX, FOREIGN KEY | Relasi ke promos.id                           |
| feed_type    | ENUM('new_campaign','hot_campaign','recommended_campaign','vendor_update') | INDEX | Jenis konten feed                             |
| title        | VARCHAR(200)       |              | Judul feed                                    |
| description  | TEXT               |              | Deskripsi feed                                |
| image_url    | VARCHAR(255)       |              | URL gambar untuk feed                         |
| action_url   | VARCHAR(255)       |              | URL aksi (ke detail campaign)                 |
| priority     | TINYINT UNSIGNED   | INDEX        | Prioritas tampil: 1=low, 5=high              |
| is_seen      | TINYINT(1)         | INDEX        | Sudah dilihat atau belum                      |
| created_at   | TIMESTAMP          | INDEX        | Waktu feed dibuat                             |
| expires_at   | TIMESTAMP          | INDEX        | Waktu feed kadaluarsa                         |

### Catatan Optimasi
- Composite index pada (kol_id, is_seen, priority DESC, created_at DESC) untuk feed timeline yang optimal
- Composite index pada (expires_at, created_at) untuk cleanup otomatis feed lama
- Foreign key dengan CASCADE DELETE
- TTL untuk auto-delete expired feeds

---

## Indexes Tambahan untuk Optimasi

```sql
-- Composite indexes untuk query kompleks vendor
CREATE INDEX idx_vendors_category_rating ON vendors(brand_category, rating DESC);
CREATE INDEX idx_vendors_verified_score ON vendors(is_verified, payment_reliability_score DESC);

-- Composite indexes untuk campaign/promo management
CREATE INDEX idx_promos_vendor_status ON promos(vendor_id, status);
CREATE INDEX idx_promos_budget_monitor ON promos(status, budget_limit, current_budget_used);
CREATE INDEX idx_promos_schedule_monitor ON promos(status, start_date, end_date);
CREATE INDEX idx_promos_active_budget ON promos(status, current_budget_used, budget_limit) WHERE status = 1;

-- Composite indexes untuk vendor reviews
CREATE INDEX idx_reviews_vendor_rating ON vendor_reviews(vendor_id, rating DESC, created_at DESC);
CREATE INDEX idx_reviews_campaign ON vendor_reviews(campaign_id, rating DESC, is_verified DESC);

-- Composite indexes untuk notification system  
CREATE INDEX idx_notifications_kol_feed ON campaign_notifications(kol_id, is_read, created_at DESC);
CREATE INDEX idx_notifications_vendor_campaign ON campaign_notifications(vendor_id, promo_id, type);

-- Composite indexes untuk KOL feed timeline
CREATE INDEX idx_feed_timeline ON kol_feed(kol_id, is_seen, priority DESC, created_at DESC);
CREATE INDEX idx_feed_cleanup ON kol_feed(expires_at, created_at);

-- Composite indexes untuk vendor follows
CREATE UNIQUE INDEX idx_follows_unique ON vendor_follows(vendor_id, kol_id);
CREATE INDEX idx_follows_kol_timeline ON vendor_follows(kol_id, created_at DESC);

-- Composite indexes untuk performance tracking
CREATE UNIQUE INDEX idx_performance_daily_unique ON vendor_performance_daily(vendor_id, date);
CREATE INDEX idx_performance_date_range ON vendor_performance_daily(date, vendor_id);

-- Full-text search untuk nama vendor dan deskripsi
ALTER TABLE vendors ADD FULLTEXT(name, about);
ALTER TABLE promos ADD FULLTEXT(nama_promo, deskripsi);
```

---

## Catatan Best Practice

### Performance Optimization
- Semua tabel menggunakan InnoDB engine
- Charset utf8mb4_unicode_ci untuk support emoji dan karakter khusus
- Partitioning pada tabel performance_daily berdasarkan date
- Regular cleanup untuk data expired/old

### Security & Data Integrity
- Foreign key constraints untuk integritas referensial
- ENUM values untuk validasi data di database level
- Proper indexing untuk mencegah table scan

### Scalability
- BIGINT untuk ID primary key (support 9 quintillion records)
- Tabel statistik terpisah untuk menghindari lock pada tabel utama
- Daily performance tracking untuk historical analysis

### Monitoring & Maintenance
- Index pada timestamp columns untuk monitoring query
- Regular ANALYZE TABLE untuk update statistics
- Monitor slow query log untuk optimasi berkelanjutan 