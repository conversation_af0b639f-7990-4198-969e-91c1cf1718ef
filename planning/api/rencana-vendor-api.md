# Rencana Implementasi API Vendor (Laravel Octane + MySQL)

## 1. Desain Database Vendor & Promo

- **Tabel `vendors`**:  
  id, name, email, phone_number, status, source (gass/klikbuy), created_at, updated_at
  
  **Field profil untuk KOL (brand attractiveness):**
  - about (deskripsi vendor/brand, untuk menampilkan profil singkat)
  - profile_pic_url (URL foto/logo vendor)
  - instagram_url, tiktok_url (opsional, link ke sosial media vendor)
  - address (alamat atau kota domisili vendor)
  - is_verified (status verifikasi vendor: 0 = belum, 1 = sudah)
  - brand_category (kategori brand: fashion, elektronik, f&b, kecantikan, dll)
  - rating (rating vendor dari 1-5, berdasarkan feedback KOL)
  - total_kol_partners (jumlah KOL yang pernah/sedang kerjasama)
  - payment_reliability_score (skor keandalan pembayaran: 1-100)
  - avg_commission_amount (rata-rata nominal komisi yang dibayarkan)
  - website_url (website resmi brand)
  - business_type (tipe bisnis: B2C, B2B, marketplace, dll)

- **Tabel `promos`**:  
  id, vendor_id, nama_promo, deskripsi,
  max_closing — batas maksimal closing untuk promo ini,
  created_at, updated_at
  
  **Target Redirect (kemana visitor akan diarahkan):**
  - target_type — tipe tujuan redirect: 'whatsapp' atau 'url'
  - target_value — nilai tujuan sesuai target_type
  
  **Penjelasan Target Type & Value:**
  - Jika target_type = 'whatsapp':
    - target_value = nomor WhatsApp (***********) atau link WA (wa.me/628123456789)
    - Visitor akan redirect ke chat WhatsApp vendor
  - Jika target_type = 'url':
    - target_value = URL tujuan (https://tokopedia.com/vendor-store)
    - Visitor akan redirect ke website/landing page vendor
  
  **Model Komisi Multi-level:**
  - commission_model (single/multi) — model komisi: single (satu jenis) atau multi (beberapa jenis)
  - komisi_per_lead — komisi untuk setiap lead/klik (opsional)
  - komisi_per_mql — komisi untuk setiap Marketing Qualified Lead (opsional)
  - komisi_per_prospek — komisi untuk setiap prospek/follow up (opsional)
  - komisi_per_closing — komisi untuk setiap closing/pembelian (opsional)
  - komisi_type (fixed/percentage) — tipe komisi: fixed (nominal) atau percentage (persentase dari purchase value)

- **Tabel `marketing_kits`**:  
  id, promo_id, nama_kit, tipe (image/video/pdf/link), url_file, created_at, updated_at

---

## 2. Endpoint API Vendor

### a. Vendor Management
- `POST /api/vendor/register` — Registrasi vendor baru (dengan field `source` wajib diisi: gass/klikbuy, tanpa password, dilakukan server-to-server)
- `GET /api/vendor/check-id` — Cek apakah vendor id/email/phone sudah terdaftar
- `GET /api/vendor` — Get profile vendor (autentikasi)
- `PUT /api/vendor` — Update profile vendor (about, profile_pic_url, sosmed, alamat, dll)
- `DELETE /api/vendor` — Hapus akun vendor

#### Statistik Vendor (ditampilkan ke KOL saat browse brand):
- total_kol_partners — jumlah KOL yang pernah/sedang kerjasama dengan brand ini
- avg_commission_amount — rata-rata nominal komisi yang dibayarkan ke KOL
- commission_range — rentang komisi (min - max): misal "Rp 50.000 - Rp 500.000"
- total_commission_paid — total komisi yang sudah dibayar Klikbuy ke KOL dari brand ini
- top_earner_this_month — KOL dengan earning tertinggi bulan ini (nama/inisial + nominal)
- campaign_budget_available — dana brand yang tersedia di Klikbuy (indikator sustainability campaign)
- total_campaign_active — jumlah campaign aktif brand ini
- payment_reliability_score — skor keandalan pembayaran brand ini (1-100) 
- top_performing_campaign — campaign dengan performa terbaik (untuk referensi KOL)


### b. Promo Management
- `POST /api/vendor/promo` — Create promo baru (wajib set: nama_promo, deskripsi, start_date, end_date, budget_limit, commission_model, target_type, target_value, max_closing, minimal 1 jenis komisi)
- `GET /api/vendor/promo` — List promo milik vendor (dengan filter: status, start_date range, budget_remaining)
- `GET /api/vendor/promo/{id}` — Detail promo (include budget usage, days remaining, status auto-stop)
- `PUT /api/vendor/promo/{id}` — Update promo (bisa update model komisi, nominal, dates, budget - sistem otomatis handle auto-stop)
- `DELETE /api/vendor/promo/{id}` — Hapus promo
- `PUT /api/vendor/promo/{id}/pause` — Pause/unpause campaign manual
- `PUT /api/vendor/promo/{id}/extend` — Extend end_date atau tambah budget campaign
- `GET /api/vendor/promo/{id}/budget-tracking` — Real-time budget usage dan proyeksi habis kapan

### c. Marketing Kit Management
- `POST /api/vendor/promo/{promo_id}/marketing-kit` — Tambah marketing kit ke promo
- `GET /api/vendor/promo/{promo_id}/marketing-kit` — List marketing kit promo
- `DELETE /api/vendor/marketing-kit/{id}` — Hapus marketing kit

### d. Endpoint untuk KOL (Public - untuk melihat brand)
- `GET /api/public/vendors` — List semua vendor/brand (untuk ditampilkan ke KOL)
  - Filter: brand_category, rating minimal, is_verified
  - Sort: rating, total_kol_partners, avg_commission_amount
  - Include: nama, logo, rating, kategori, total_kol_partners, avg_commission
- `GET /api/public/vendor/{id}` — Detail lengkap vendor/brand untuk KOL
  - Include: semua field profil, statistik yang relevan untuk KOL, list promo aktif
- `GET /api/public/vendor/{id}/promos` — List promo aktif dari vendor tertentu

### e. Endpoint untuk Update Statistik Vendor
- `PUT /api/vendor/{id}/stats/update` — Update statistik vendor (internal use, dipanggil otomatis sistem)
- `POST /api/vendor/{id}/rating` — KOL kasih rating ke vendor setelah campaign selesai
- `PUT /api/vendor/{id}/stats/recalculate` — Recalculate semua statistik vendor (maintenance)

### f. Endpoint untuk KOL Interaction dengan Vendor
- `POST /api/public/vendor/{id}/follow` — KOL follow/bookmark vendor
- `DELETE /api/public/vendor/{id}/follow` — KOL unfollow vendor
- `GET /api/public/kol/{kol_id}/followed-vendors` — List vendor yang di-follow KOL
- `POST /api/public/vendor/{id}/review` — KOL kasih review/feedback ke vendor
- `GET /api/public/vendor/{id}/reviews` — List review dari KOL lain (dengan pagination)
- `PUT /api/public/vendor/{id}/review/{review_id}` — Update review KOL
- `DELETE /api/public/vendor/{id}/review/{review_id}` — Hapus review KOL

### g. Campaign Notifications & Feed API
- `GET /api/kol/notifications` — List notifikasi untuk KOL (new campaigns dari vendor yang di-follow)
  - Filter: is_read, type, date_range
  - Sort: created_at DESC, priority DESC
- `PUT /api/kol/notifications/{id}/read` — Mark notifikasi sebagai sudah dibaca
- `PUT /api/kol/notifications/mark-all-read` — Mark semua notifikasi sebagai sudah dibaca
- `DELETE /api/kol/notifications/{id}` — Hapus notifikasi tertentu
- `GET /api/kol/feed` — Feed timeline campaign baru/update untuk KOL
  - Include: new campaigns dari vendor yang di-follow, hot campaigns, recommended campaigns
  - Pagination dengan infinite scroll
  - Filter: feed_type, vendor category, priority
- `PUT /api/kol/feed/{id}/seen` — Mark feed item sebagai sudah dilihat
- `GET /api/kol/feed/unread-count` — Count notifikasi dan feed yang belum dibaca
- `POST /api/system/send-campaign-notification` — Internal API untuk trigger notifikasi ke followers (otomatis dipanggil saat vendor create campaign baru)

### h. Campaign Automation & Monitoring
- `POST /api/system/check-campaign-budgets` — Cron job untuk cek budget dan auto-stop campaign yang habis
- `POST /api/system/check-campaign-dates` — Cron job untuk auto-stop campaign yang sudah end_date
- `GET /api/system/campaigns/expiring-soon` — List campaign yang akan habis budget/tanggal dalam 24-48 jam (untuk alert vendor)
- `PUT /api/system/campaign/{id}/auto-stop` — Auto-stop campaign dengan alasan (budget_exhausted/date_ended)
- `GET /api/vendor/dashboard/alerts` — Dashboard alerts untuk vendor (budget hampir habis, campaign performance, dll)

### i. Endpoint untuk Advanced Search & Filter
- `GET /api/public/vendors/search` — Search vendor dengan multiple criteria
  - Query params: keyword, category, min_commission, max_commission, min_rating, location, commission_type, business_type, is_verified
  - Advanced filters: campaign_budget_min
- `GET /api/public/vendors/recommended` — Rekomendasi vendor untuk KOL berdasarkan profile
  - Berdasarkan: kategori interest KOL, history campaign, audience demographic
- `GET /api/public/vendors/trending` — Vendor yang sedang trending (berdasarkan aktivitas KOL)
- `GET /api/public/vendors/new` — Vendor baru yang baru join platform

### j. Endpoint untuk Campaign Performance Tracking
- `GET /api/public/vendor/{id}/live-performance` — Live performance data vendor hari ini
- `GET /api/public/promo/{id}/analytics` — Detail analytics promo tertentu
- `GET /api/public/vendor/{id}/performance-history` — History performa vendor per bulan
- `GET /api/public/campaign-leaderboard` — Leaderboard campaign/vendor berdasarkan performa

---

## 3. Fitur Khusus Promo
- **Model Komisi Multi-level** - Vendor bisa set komisi untuk berbagai tahap funnel:
  - **Per Lead**: Komisi setiap klik/lead yang masuk (misal: Rp 1.000 per klik)
  - **Per MQL**: Komisi untuk Marketing Qualified Lead (misal: yang isi form lengkap)
  - **Per Prospek**: Komisi untuk prospek yang follow up/chat (misal: yang chat WA)
  - **Per Closing**: Komisi untuk closing/pembelian final
  - **Commission Model**: single (pilih 1 jenis) atau multi (kombinasi beberapa jenis)
  - **Tipe Komisi**: fixed (nominal tetap) atau percentage (persentase dari purchase value)
- **Target Redirect Promo** - Menentukan kemana visitor diarahkan setelah input kode:
  - **WhatsApp**: Langsung ke chat WA vendor (untuk konsultasi/order via WA)
  - **URL**: Ke website/landing page vendor (untuk order online/lihat produk)
- Marketing kit bisa berupa file (image, video, pdf) atau link
- Promo bisa dibatasi maksimal berapa closing (`max_closing`)

---

## 4. Middleware & Proteksi
- Gunakan Laravel Sanctum/Passport untuk autentikasi vendor
- Middleware khusus role vendor

---

## 5. Validasi & Error Handling
- Validasi input (email/phone unik, format file marketing kit, dsb)
- Validasi field `source` hanya boleh berisi 'gass' atau 'klikbuy'
- Validasi field `target_type` hanya boleh berisi 'whatsapp' atau 'url'
- Validasi field `target_value` harus sesuai dengan target_type:
  - Jika target_type = 'whatsapp': validasi format nomor/link WA
  - Jika target_type = 'url': validasi format URL yang benar
- Validasi field `max_closing` harus angka positif
- Validasi field `commission_model` hanya boleh berisi 'single' atau 'multi'
- Validasi minimal 1 jenis komisi harus diisi (lead/mql/prospek/closing)
- **Validasi Campaign Scheduling:**
  - `start_date` tidak boleh di masa lalu (minimal H+1 dari create)
  - `end_date` harus lebih besar dari `start_date` (minimal 1 hari gap)
  - `budget_limit` harus angka positif dan minimal Rp 50.000
  - `current_budget_used` tidak boleh melebihi `budget_limit`
  - Campaign yang sudah `status=3 (ended)` atau `status=4 (budget_exhausted)` tidak bisa di-update tanggal/budget
- **Validasi Budget Management:**
  - Saat extend budget: budget baru harus lebih besar dari current budget
  - Saat extend end_date: tanggal baru harus lebih besar dari end_date sekarang
  - Sistem otomatis stop campaign jika budget habis atau end_date tercapai
- Validasi field `brand_category` harus dari list kategori yang sudah ditentukan
- Validasi field `rating` harus antara 1-5
- Validasi field `payment_reliability_score` harus antara 1-100
- Validasi field `business_type` harus dari list tipe bisnis yang sudah ditentukan
- Validasi rating vendor (1-5) dan review tidak boleh kosong
- Validasi follow/unfollow vendor (KOL tidak bisa follow vendor yang sama 2x)
- Validasi search parameters (min_commission tidak boleh negatif, dll)
- **Validasi Notification & Feed:**
  - KOL hanya bisa dapat notifikasi dari vendor yang di-follow
  - Feed item hanya tampil untuk KOL yang relevan (berdasarkan interest/category)
  - Notification type harus sesuai ENUM yang sudah ditentukan
- Standarisasi response JSON (success/error/message/data)

---

## 6. Testing
- Unit test & feature test untuk setiap endpoint

---

## 7. Dokumentasi
- Buat dokumentasi endpoint (Swagger/OpenAPI atau manual markdown)

---

**Catatan:**
- Semua endpoint harus dioptimalkan untuk performa (Octane-ready)
- Pastikan keamanan data vendor & promo
- Siapkan seed vendor & promo dummy untuk testing awal
- Field `source` pada vendor untuk menandai asal vendor (gass/klikbuy)
- Pembuatan vendor dilakukan server-to-server, bukan self-service, sehingga tidak ada password dan endpoint login.
- Field `target_type` dan `target_value` pada promo WAJIB diisi dan harus sesuai keterangan di atas.
- Field `max_closing` pada promo untuk membatasi maksimal closing.
- **Campaign Scheduling & Budget Management:**
  - Vendor wajib set `start_date`, `end_date`, dan `budget_limit` saat create campaign
  - Sistem otomatis start campaign pada `start_date`
  - Sistem SELALU otomatis stop campaign jika `current_budget_used >= budget_limit` (tidak bisa di-disable)
  - Sistem SELALU otomatis stop campaign pada `end_date` (tidak bisa di-disable)
  - Real-time budget tracking: setiap komisi dibayar ke KOL, `current_budget_used` otomatis bertambah
  - Status campaign: 0=draft, 1=active, 2=paused, 3=ended, 4=budget_exhausted
  - Cron job berjalan setiap 5 menit untuk cek campaign yang perlu di-auto-stop
- **Follow Vendor & Notification System:**
  - KOL bisa follow/unfollow vendor untuk dapat update campaign terbaru
  - Setiap vendor create campaign baru, sistem otomatis kirim notifikasi ke semua followers
  - Feed KOL menampilkan campaign baru dari vendor yang di-follow + recommended campaigns
  - Notification vs Feed: Notification = alert/popup, Feed = timeline content
  - Auto-cleanup notification/feed lama (> 30 hari) untuk optimasi storage
- **Flow Pembayaran Komisi:**
  - Vendor bayar dana campaign ke Klikbuy/Gass terlebih dahulu (prepaid system)
  - Klikbuy/Gass otomatis bayar komisi ke KOL real-time sesuai performance
  - Tidak ada pending payment karena dana sudah tersedia di platform
  - KOL bisa lihat campaign_budget_available untuk assess sustainability campaign
  - Sistem ini menjamin KOL pasti dibayar (no payment risk)
- **Model Komisi Multi-level** memberikan fleksibilitas pembayaran:
  - Vendor bisa bayar KOL di setiap tahap funnel (lead, MQL, prospek, closing)
  - KOL bisa dapat income bahkan tanpa closing (misal: dari lead/prospek)
  - Model 'single' untuk promo sederhana, 'multi' untuk promo kompleks
- **Field profil vendor dirancang khusus untuk menarik minat KOL:**
  - Rating dan payment_reliability_score untuk menunjukkan kredibilitas brand
  - brand_category untuk memudahkan KOL cari brand sesuai niche mereka
  - commission_range dan top_earner_this_month untuk memberikan gambaran earning potential yang jelas
  - total_kol_partners dan avg_commission_amount untuk menunjukkan popularitas brand
  - Semua statistik yang ditampilkan ke KOL harus real-time dan akurat 