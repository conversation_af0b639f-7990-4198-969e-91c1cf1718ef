import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { registerUser } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface FormErrors {
  name?: string
  email?: string
  phone_number?: string
  password?: string
  password2?: string
  country?: string
  city?: string
}

export const RegisterForm: React.FC = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone_number: '',
    password: '',
    password2: '',
    country: '',
    city: '',
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isLoading, setIsLoading] = useState(false)
  const [successMsg, setSuccessMsg] = useState('')
  const [errorMsg, setErrorMsg] = useState('')

  const validateForm = (): boolean => {
    const errors: FormErrors = {}
    if (!formData.name) errors.name = 'Nama wajib diisi'
    if (!formData.email && !formData.phone_number) errors.email = 'Email atau No HP wajib diisi'
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) errors.email = 'Format email tidak valid'
    if (formData.phone_number && !/^\d{8,15}$/.test(formData.phone_number)) errors.phone_number = 'No HP tidak valid'
    if (!formData.password) errors.password = 'Password wajib diisi'
    else if (formData.password.length < 6) errors.password = 'Password minimal 6 karakter'
    if (!formData.password2) errors.password2 = 'Ulangi password wajib diisi'
    else if (formData.password !== formData.password2) errors.password2 = 'Password tidak sama'
    if (!formData.country) errors.country = 'Negara wajib diisi'
    if (!formData.city) errors.city = 'Kota wajib diisi'
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSuccessMsg('')
    setErrorMsg('')
    if (!validateForm()) return
    setIsLoading(true)
    try {
      await registerUser({
        name: formData.name,
        email: formData.email || undefined,
        phone_number: formData.phone_number || undefined,
        password: formData.password,
        city: formData.city,
        country: formData.country,
      })
      setSuccessMsg('Registrasi berhasil! Mengarahkan ke halaman login...')
      setFormData({ name: '', email: '', phone_number: '', password: '', password2: '', country: '', city: '' })
      setTimeout(() => {
        navigate('/login', { 
          state: { message: 'Registrasi berhasil! Silakan login dengan akun Anda.' }
        })
      }, 2000)
    } catch (err: any) {
      if (err && err.message === 'Validasi gagal' && err.errors) {
        const beErrors = err.errors
        const newFormErrors: any = {}
        Object.keys(beErrors).forEach(field => {
          newFormErrors[field] = Array.isArray(beErrors[field]) ? beErrors[field][0] : beErrors[field]
        })
        setFormErrors((prev) => ({ ...prev, ...newFormErrors }))
        setErrorMsg('')
      } else {
        setErrorMsg(err.message || 'Registrasi gagal')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    if (formErrors[name as keyof FormErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-12 px-4">
      <div className="w-full max-w-lg">
        <Card className="shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-center text-2xl font-bold text-gray-900 mb-2">
              Register Account
            </CardTitle>
          </CardHeader>
          
          <CardContent className="pt-0 px-8 pb-8">
            {successMsg && (
              <Alert variant="default" className="mb-6 border-green-200 bg-green-50">
                <AlertDescription className="text-green-700">{successMsg}</AlertDescription>
              </Alert>
            )}
            {errorMsg && (
              <Alert variant="destructive" className="mb-6 border-red-200 bg-red-50">
                <AlertDescription className="text-red-700">{errorMsg}</AlertDescription>
              </Alert>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                  Nama Lengkap
                </Label>
                <Input 
                  id="name" 
                  name="name" 
                  value={formData.name} 
                  onChange={handleInputChange} 
                  placeholder="Masukkan nama lengkap"
                  disabled={isLoading} 
                  className={`h-11 ${formErrors.name ? 'border-red-300 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'} transition-colors`}
                />
                {formErrors.name && <p className="text-xs text-red-600 mt-1">{formErrors.name}</p>}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Email
                </Label>
                <Input 
                  id="email" 
                  name="email" 
                  type="email" 
                  value={formData.email} 
                  onChange={handleInputChange} 
                  placeholder="<EMAIL>"
                  disabled={isLoading} 
                  className={`h-11 ${formErrors.email ? 'border-red-300 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'} transition-colors`}
                />
                {formErrors.email && <p className="text-xs text-red-600 mt-1">{formErrors.email}</p>}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone_number" className="text-sm font-medium text-gray-700">
                  Nomor HP
                </Label>
                <Input 
                  id="phone_number" 
                  name="phone_number" 
                  value={formData.phone_number} 
                  onChange={handleInputChange} 
                  placeholder="081234567890"
                  disabled={isLoading} 
                  className={`h-11 ${formErrors.phone_number ? 'border-red-300 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'} transition-colors`}
                />
                {formErrors.phone_number && <p className="text-xs text-red-600 mt-1">{formErrors.phone_number}</p>}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                  Password
                </Label>
                <Input 
                  id="password" 
                  name="password" 
                  type="password" 
                  value={formData.password} 
                  onChange={handleInputChange} 
                  placeholder="Minimal 6 karakter"
                  disabled={isLoading} 
                  className={`h-11 ${formErrors.password ? 'border-red-300 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'} transition-colors`}
                />
                {formErrors.password && <p className="text-xs text-red-600 mt-1">{formErrors.password}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="password2" className="text-sm font-medium text-gray-700">
                  Ulangi Password
                </Label>
                <Input 
                  id="password2" 
                  name="password2" 
                  type="password" 
                  value={formData.password2} 
                  onChange={handleInputChange} 
                  placeholder="Ulangi password"
                  disabled={isLoading} 
                  className={`h-11 ${formErrors.password2 ? 'border-red-300 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'} transition-colors`}
                />
                {formErrors.password2 && <p className="text-xs text-red-600 mt-1">{formErrors.password2}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="country" className="text-sm font-medium text-gray-700">
                  Negara
                </Label>
                <Input 
                  id="country" 
                  name="country" 
                  value={formData.country} 
                  onChange={handleInputChange} 
                  placeholder="Indonesia"
                  disabled={isLoading} 
                  className={`h-11 ${formErrors.country ? 'border-red-300 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'} transition-colors`}
                />
                {formErrors.country && <p className="text-xs text-red-600 mt-1">{formErrors.country}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="city" className="text-sm font-medium text-gray-700">
                  Kota
                </Label>
                <Input 
                  id="city" 
                  name="city" 
                  value={formData.city} 
                  onChange={handleInputChange} 
                  placeholder="Jakarta"
                  disabled={isLoading} 
                  className={`h-11 ${formErrors.city ? 'border-red-300 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'} transition-colors`}
                />
                {formErrors.city && <p className="text-xs text-red-600 mt-1">{formErrors.city}</p>}
              </div>
              
              <Button 
                type="submit" 
                className="w-full h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200" 
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Memproses...
                  </div>
                ) : (
                  'Daftar Akun'
                )}
              </Button>
            </form>
            
            <div className="mt-6 space-y-3">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200"></div>
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">atau</span>
                </div>
              </div>
              
              <div className="text-center">
                <span className="text-sm text-gray-600">Sudah punya akun? </span>
                <a href="/login" className="text-sm text-blue-600 hover:text-blue-700 font-medium hover:underline transition-colors">
                  Masuk di sini
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="text-center mt-6">
          <p className="text-xs text-gray-500">
            Dengan mendaftar, Anda menyetujui <a href="/terms" className="text-blue-600 hover:underline">Syarat & Ketentuan</a> kami
          </p>
        </div>
      </div>
    </div>
  )
} 