import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/context/AuthContext'
import { useLocation } from 'react-router-dom'

export const LoginForm: React.FC = () => {
  const { login, isLoading, error, clearError } = useAuth()
  const location = useLocation()
  const [formData, setFormData] = useState({ emailOrPhone: '', password: '' })
  const [formErrors, setFormErrors] = useState<{ emailOrPhone?: string, password?: string }>({})
  const [successMessage, setSuccessMessage] = useState('')

  // Cek pesan dari redirect (misal dari register)
  useEffect(() => {
    if (location.state?.message) {
      setSuccessMessage(location.state.message)
      // Clear state agar tidak muncul lagi saat refresh
      window.history.replaceState({}, document.title)
    }
  }, [location])

  const validateForm = () => {
    const errors: typeof formErrors = {}
    if (!formData.emailOrPhone) {
      errors.emailOrPhone = 'Email atau nomor HP wajib diisi'
    } else {
      // Auto-detect: jika ada @ berarti email, jika tidak berarti nomor HP
      const isEmail = formData.emailOrPhone.includes('@')
      if (isEmail && !/\S+@\S+\.\S+/.test(formData.emailOrPhone)) {
        errors.emailOrPhone = 'Format email tidak valid'
      } else if (!isEmail && !/^\d{8,15}$/.test(formData.emailOrPhone)) {
        errors.emailOrPhone = 'Nomor HP tidak valid (8-15 digit)'
      }
    }
    if (!formData.password) {
      errors.password = 'Password wajib diisi'
    } else if (formData.password.length < 6) {
      errors.password = 'Password minimal 6 karakter'
    }
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }))
    }
    if (error) clearError()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return
    const payload: any = { password: formData.password }
    // Auto-detect: jika ada @ berarti email, jika tidak berarti nomor HP
    if (formData.emailOrPhone.includes('@')) {
      payload.email = formData.emailOrPhone
    } else {
      payload.phone_number = formData.emailOrPhone
    }
    await login(payload)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-12 px-4">
      <div className="w-full max-w-lg">
      
        <Card className="shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-center text-2xl font-bold text-gray-900 mb-2">
              Login
            </CardTitle>
          </CardHeader>
          
          <CardContent className="pt-0 px-8 pb-8">
            {error && (
              <Alert variant="destructive" className="mb-6 border-red-200 bg-red-50">
                <AlertDescription className="text-red-700">{error}</AlertDescription>
              </Alert>
            )}
            {successMessage && (
              <Alert variant="default" className="mb-6 border-green-200 bg-green-50">
                <AlertDescription className="text-green-700">{successMessage}</AlertDescription>
              </Alert>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="space-y-2">
                <Label htmlFor="emailOrPhone" className="text-sm font-medium text-gray-700">
                  Email atau Nomor HP
                </Label>
                <Input
                  id="emailOrPhone"
                  name="emailOrPhone"
                  type="text"
                  autoComplete="username"
                  value={formData.emailOrPhone}
                  onChange={handleInputChange}
                  placeholder="<EMAIL> atau 081234567890"
                  disabled={isLoading}
                  className={`h-11 ${formErrors.emailOrPhone ? 'border-red-300 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'} transition-colors`}
                />
                {formErrors.emailOrPhone && <p className="text-xs text-red-600 mt-1">{formErrors.emailOrPhone}</p>}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                  Password
                </Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="Masukkan password Anda"
                  disabled={isLoading}
                  className={`h-11 ${formErrors.password ? 'border-red-300 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'} transition-colors`}
                />
                {formErrors.password && <p className="text-xs text-red-600 mt-1">{formErrors.password}</p>}
              </div>
              
              <Button 
                type="submit" 
                className="w-full h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200" 
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Memproses...
                  </div>
                ) : (
                  'Masuk ke Akun'
                )}
              </Button>
            </form>
            
            <div className="mt-6 space-y-3">
              <div className="text-center">
                <a href="/forgot-password" className="text-sm text-blue-600 hover:text-blue-700 hover:underline transition-colors">
                  Lupa password?
                </a>
              </div>
              
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200"></div>
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">atau</span>
                </div>
              </div>
              
              <div className="text-center">
                <span className="text-sm text-gray-600">Belum punya akun? </span>
                <a href="/register" className="text-sm text-blue-600 hover:text-blue-700 font-medium hover:underline transition-colors">
                  Daftar sekarang
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="text-center mt-6">
          <p className="text-xs text-gray-500">
            Dengan masuk, Anda menyetujui <a href="/terms" className="text-blue-600 hover:underline">Syarat & Ketentuan</a> kami
          </p>
        </div>
      </div>
    </div>
  )
} 