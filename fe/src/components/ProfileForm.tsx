import React, { useState, useEffect } from 'react'
import { useAuth } from '@/context/AuthContext'
import { updateProfile, updateSocialMedia, updatePayment } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Instagram, 
  CreditCard, 
  Plus, 
  Trash2, 
  Save,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

// Tipe untuk error form
interface FormErrors {
  name?: string
  email?: string
  phone_number?: string
  city?: string
  country?: string
  social_media?: string
  payment?: string
}

// Tipe data sosial media
interface SocialMediaItem {
  platform: string
  username_or_url: string
}

// Tipe data pembayaran
interface PaymentItem {
  bank_name: string
  account_number: string
  account_holder: string
}

// Komponen untuk field input dengan icon
interface InputFieldProps {
  label: string
  icon: React.ReactNode
  id: string
  name: string
  type?: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  placeholder: string
  disabled?: boolean
  error?: string
  required?: boolean
}

const InputField: React.FC<InputFieldProps> = ({
  label,
  icon,
  id,
  name,
  type = 'text',
  value,
  onChange,
  placeholder,
  disabled = false,
  error,
  required = false
}) => (
  <div className="space-y-2">
    <Label htmlFor={id} className="flex items-center gap-2 text-sm font-medium text-gray-700">
      {icon}
      {label}
      {required && <span className="text-red-500">*</span>}
    </Label>
    <div className="relative">
      <Input
        id={id}
        name={name}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        className={`
          h-11 transition-all duration-200
          ${error 
            ? 'border-red-300 focus:border-red-500 focus:ring-red-200' 
            : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'
          }
          ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}
        `}
        aria-describedby={error ? `${id}-error` : undefined}
        aria-invalid={!!error}
      />
    </div>
    {error && (
      <p id={`${id}-error`} className="text-xs text-red-600 flex items-center gap-1">
        <AlertCircle className="h-3 w-3" />
        {error}
      </p>
    )}
  </div>
)

// Komponen untuk section card
interface SectionCardProps {
  title: string
  description: string
  icon: React.ReactNode
  children: React.ReactNode
  error?: string
}

const SectionCard: React.FC<SectionCardProps> = ({
  title,
  description,
  icon,
  children,
  error
}) => (
  <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
    <CardHeader className="pb-4">
      <CardTitle className="flex items-center gap-3 text-lg font-semibold text-gray-900">
        <div className="p-2 bg-blue-50 rounded-lg">
          {icon}
        </div>
        <div>
          <div>{title}</div>
          <p className="text-sm font-normal text-gray-500 mt-1">{description}</p>
        </div>
      </CardTitle>
      {error && (
        <Alert variant="destructive" className="mt-3">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </CardHeader>
    <CardContent className="pt-0">
      {children}
    </CardContent>
  </Card>
)

export const ProfileForm: React.FC = () => {
  const { user } = useAuth()
  
  // State data pribadi
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone_number: '',
    city: '',
    country: '',
  })
  
  // State sosial media (bisa lebih dari satu)
  const [socialMedia, setSocialMedia] = useState<SocialMediaItem[]>([
    { platform: '', username_or_url: '' },
  ])
  
  // State pembayaran (hanya satu data)
  const [payment, setPayment] = useState<PaymentItem>({
    bank_name: '',
    account_number: '',
    account_holder: '',
  })
  
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isLoading, setIsLoading] = useState(false)
  const [loadingStates, setLoadingStates] = useState({
    profile: false,
    socialMedia: false,
    payment: false
  })
  const [successMsg, setSuccessMsg] = useState('')
  const [errorMsg, setErrorMsg] = useState('')

  // Populate form dengan data user saat mount
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        phone_number: user.phone_number || '',
        city: user.city || '',
        country: user.country || '',
      })
      
      // Data sosial media (jika ada)
      if (user.social_media && Array.isArray(user.social_media) && user.social_media.length > 0) {
        setSocialMedia(user.social_media.map((sm: any) => ({
          platform: sm.platform || '',
          username_or_url: sm.username_or_url || '',
        })))
      }
      
      // Data pembayaran (jika ada)
      if (user.tujuan_pembayaran && Array.isArray(user.tujuan_pembayaran) && user.tujuan_pembayaran.length > 0) {
        setPayment({
          bank_name: user.tujuan_pembayaran[0].bank_name || '',
          account_number: user.tujuan_pembayaran[0].account_number || '',
          account_holder: user.tujuan_pembayaran[0].account_holder || '',
        })
      }
    }
  }, [user])

  // Validasi form
  const validateForm = (): boolean => {
    const errors: FormErrors = {}
    if (!formData.name.trim()) errors.name = 'Nama wajib diisi'
    if (!formData.email && !formData.phone_number) {
      errors.email = 'Email atau No HP wajib diisi'
    }
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Format email tidak valid'
    }
    if (formData.phone_number && !/^\d{8,15}$/.test(formData.phone_number)) {
      errors.phone_number = 'No HP harus 8-15 digit angka'
    }
    if (!formData.country.trim()) errors.country = 'Negara wajib diisi'
    if (!formData.city.trim()) errors.city = 'Kota wajib diisi'
    
    // Validasi sosial media (hanya yang diisi)
    const filledSocialMedia = socialMedia.filter(sm => sm.platform || sm.username_or_url)
    if (filledSocialMedia.some(sm => !sm.platform || !sm.username_or_url)) {
      errors.social_media = 'Jika mengisi sosial media, platform dan username/URL wajib diisi'
    }
    
    // Validasi pembayaran (hanya satu)
    if (payment.bank_name || payment.account_number || payment.account_holder) {
      if (!payment.bank_name || !payment.account_number || !payment.account_holder) {
        errors.payment = 'Jika mengisi data pembayaran, semua field wajib diisi'
      }
    }
    
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSuccessMsg('')
    setErrorMsg('')
    
    if (!validateForm()) {
      setErrorMsg('Mohon perbaiki kesalahan pada form')
      return
    }
    
    setIsLoading(true)
    
    try {
      // Update data pribadi
      setLoadingStates(prev => ({ ...prev, profile: true }))
      await updateProfile({
        name: formData.name,
        email: formData.email || undefined,
        phone_number: formData.phone_number || undefined,
        city: formData.city,
        country: formData.country,
      })
      
      // Update sosial media (hanya yang diisi)
      const validSocialMedia = socialMedia.filter(sm => sm.platform && sm.username_or_url)
      if (validSocialMedia.length > 0) {
        setLoadingStates(prev => ({ ...prev, socialMedia: true }))
        await updateSocialMedia({
          social_media: validSocialMedia,
        })
      }
      
      // Update pembayaran (hanya jika diisi lengkap)
      if (payment.bank_name && payment.account_number && payment.account_holder) {
        setLoadingStates(prev => ({ ...prev, payment: true }))
        await updatePayment({
          tujuan_pembayaran: [payment],
        })
      }
      
      setSuccessMsg('Profil berhasil diperbarui!')
      
      // Scroll to top untuk melihat success message
      window.scrollTo({ top: 0, behavior: 'smooth' })
      
    } catch (err: any) {
      if (err && err.message === 'Validasi gagal' && err.errors) {
        const beErrors = err.errors
        const newFormErrors: any = {}
        Object.keys(beErrors).forEach(field => {
          newFormErrors[field] = Array.isArray(beErrors[field]) ? beErrors[field][0] : beErrors[field]
        })
        setFormErrors((prev) => ({ ...prev, ...newFormErrors }))
        setErrorMsg('Terdapat kesalahan validasi dari server')
      } else {
        setErrorMsg(err.message || 'Terjadi kesalahan saat menyimpan data')
      }
    } finally {
      setIsLoading(false)
      setLoadingStates({ profile: false, socialMedia: false, payment: false })
    }
  }

  // Handler input data pribadi
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    if (formErrors[name as keyof FormErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  // Handler sosial media
  const handleSocialMediaChange = (idx: number, field: keyof SocialMediaItem, value: string) => {
    setSocialMedia(prev => prev.map((sm, i) => i === idx ? { ...sm, [field]: value } : sm))
    setFormErrors(prev => ({ ...prev, social_media: undefined }))
  }
  
  const addSocialMedia = () => {
    setSocialMedia(prev => [...prev, { platform: '', username_or_url: '' }])
  }
  
  const removeSocialMedia = (idx: number) => {
    if (socialMedia.length > 1) {
      setSocialMedia(prev => prev.filter((_, i) => i !== idx))
    }
  }

  // Handler pembayaran
  const handlePaymentChange = (field: keyof PaymentItem, value: string) => {
    setPayment(prev => ({ ...prev, [field]: value }))
    setFormErrors(prev => ({ ...prev, payment: undefined }))
  }

  // Clear messages after 5 seconds
  useEffect(() => {
    if (successMsg || errorMsg) {
      const timer = setTimeout(() => {
        setSuccessMsg('')
        setErrorMsg('')
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [successMsg, errorMsg])

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Edit Profil KOL
          </h1>
          <p className="text-gray-600">
            Kelola informasi profil dan data pembayaran Anda
          </p>
        </div>

        {/* Alert Messages */}
            {successMsg && (
          <Alert className="mb-6 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-700">{successMsg}</AlertDescription>
              </Alert>
            )}
        
            {errorMsg && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{errorMsg}</AlertDescription>
              </Alert>
            )}
            
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* SECTION: Data Pribadi */}
          <SectionCard
            title="Data Pribadi"
            description="Informasi dasar profil Anda"
            icon={<User className="h-5 w-5 text-blue-600" />}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <InputField
                label="Nama Lengkap"
                icon={<User className="h-4 w-4" />}
                  id="name" 
                  name="name" 
                  value={formData.name} 
                  onChange={handleInputChange} 
                  placeholder="Masukkan nama lengkap"
                  disabled={isLoading} 
                error={formErrors.name}
                required
              />
              
              <InputField
                label="Email"
                icon={<Mail className="h-4 w-4" />}
                  id="email" 
                  name="email" 
                  type="email" 
                  value={formData.email} 
                  onChange={handleInputChange} 
                  placeholder="<EMAIL>"
                  disabled={isLoading} 
                error={formErrors.email}
              />
              
              <InputField
                label="Nomor HP/WA"
                icon={<Phone className="h-4 w-4" />}
                  id="phone_number" 
                  name="phone_number" 
                  value={formData.phone_number} 
                  onChange={handleInputChange} 
                  placeholder="08**********"
                  disabled={isLoading} 
                error={formErrors.phone_number}
              />
              
              <InputField
                label="Negara"
                icon={<Globe className="h-4 w-4" />}
                  id="country" 
                  name="country" 
                  value={formData.country} 
                  onChange={handleInputChange} 
                  placeholder="Indonesia"
                  disabled={isLoading} 
                error={formErrors.country}
                required
              />
              
              <div className="md:col-span-1">
                <InputField
                  label="Kota"
                  icon={<MapPin className="h-4 w-4" />}
                  id="city" 
                  name="city" 
                  value={formData.city} 
                  onChange={handleInputChange} 
                  placeholder="Jakarta"
                  disabled={isLoading} 
                  error={formErrors.city}
                  required
                />
              </div>
            </div>
          </SectionCard>

          {/* SECTION: Data Sosial Media */}
          <SectionCard
            title="Sosial Media"
            description="Tambahkan akun sosial media Anda (opsional)"
            icon={<Instagram className="h-5 w-5 text-blue-600" />}
            error={formErrors.social_media}
          >
            <div className="space-y-4">
              {socialMedia.map((sm, idx) => (
                <div key={idx} className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                    <div className="md:col-span-2">
                      <Label className="text-sm font-medium text-gray-700">Platform</Label>
                      <Input
                        value={sm.platform}
                        onChange={e => handleSocialMediaChange(idx, 'platform', e.target.value)}
                        placeholder="Instagram, TikTok, YouTube"
                        disabled={isLoading}
                        className="mt-1"
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <Label className="text-sm font-medium text-gray-700">Username/URL</Label>
                      <Input
                        value={sm.username_or_url}
                        onChange={e => handleSocialMediaChange(idx, 'username_or_url', e.target.value)}
                        placeholder="@username atau link profil"
                        disabled={isLoading}
                        className="mt-1"
                      />
                    </div>
                    
                    <div className="flex gap-2">
                      {socialMedia.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeSocialMedia(idx)}
                          disabled={isLoading}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                      
                      {idx === socialMedia.length - 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addSocialMedia}
                          disabled={isLoading}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </SectionCard>

          {/* SECTION: Data Pembayaran */}
          <SectionCard
            title="Data Pembayaran"
            description="Informasi rekening untuk penerimaan komisi (opsional)"
            icon={<CreditCard className="h-5 w-5 text-blue-600" />}
            error={formErrors.payment}
          >
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Nama Bank</Label>
                  <Input
                    value={payment.bank_name}
                    onChange={e => handlePaymentChange('bank_name', e.target.value)}
                    placeholder="BCA, Mandiri, dll"
                    disabled={isLoading}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">No. Rekening</Label>
                  <Input
                    value={payment.account_number}
                    onChange={e => handlePaymentChange('account_number', e.target.value)}
                    placeholder="**********"
                    disabled={isLoading}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Nama Pemilik</Label>
                  <Input
                    value={payment.account_holder}
                    onChange={e => handlePaymentChange('account_holder', e.target.value)}
                    placeholder="Sesuai buku tabungan"
                    disabled={isLoading}
                    className="mt-1"
                  />
                </div>
              </div>
            </div>
          </SectionCard>

          {/* Submit Button */}
          <div className="flex justify-center pt-4">
            <Button
              type="submit"
              size="lg"
              disabled={isLoading}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Menyimpan Perubahan...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Simpan Perubahan
                </>
                )}
              </Button>
          </div>
            </form>
      </div>
    </div>
  )
} 