const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api'

export async function apiFetch(path: string, options: RequestInit = {}) {
  const token = localStorage.getItem('token')
  const headers = {
    'Content-Type': 'application/json',
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...(options.headers || {}),
  }
  const res = await fetch(`${API_URL}${path}`, {
    ...options,
    headers,
  })
  let data
  try {
    data = await res.json()
  } catch {
    data = null
  }
  if (!res.ok) {
    if (data && typeof data === 'object' && data.errors) {
      throw { message: data.message || 'Terjadi kesalahan', errors: data.errors }
    }
    throw new Error(data?.message || 'Terjadi kesalahan')
  }
  return data
} 

// AUTH & VERIFIKASI
export function registerUser(data: { name: string; email?: string; phone_number?: string; password: string; city?: string; country?: string }) {
  return apiFetch('/register', { method: 'POST', body: JSON.stringify(data) })
}

export function requestOtp(data: { phone_number: string; type: string }) {
  return apiFetch('/request-otp', { method: 'POST', body: JSON.stringify(data) })
}

export function verifyOtp(data: { phone_number: string; otp_code: string; type: string }) {
  return apiFetch('/verify-otp', { method: 'POST', body: JSON.stringify(data) })
}

export function resendEmail() {
  return apiFetch('/email/resend', { method: 'POST' })
}

export function forgotPassword(data: { email?: string; phone_number?: string }) {
  return apiFetch('/forgot-password', { method: 'POST', body: JSON.stringify(data) })
}

export function resetPassword(data: { token: string; password: string; password_confirmation: string }) {
  return apiFetch('/reset-password', { method: 'POST', body: JSON.stringify(data) })
}

// USER MANAGEMENT
export function updateProfile(data: { name?: string; email?: string; phone_number?: string; city?: string; country?: string }) {
  return apiFetch('/user', { method: 'PUT', body: JSON.stringify(data) })
}

export function deleteAccount() {
  return apiFetch('/user', { method: 'DELETE' })
}

export function updatePayment(data: { tujuan_pembayaran: any[] }) {
  return apiFetch('/user/payment', { method: 'PUT', body: JSON.stringify(data) })
}

export function updateSocialMedia(data: { social_media: { platform: string; username_or_url: string }[] }) {
  return apiFetch('/user/social-media', { method: 'PUT', body: JSON.stringify(data) })
} 