import React, { createContext, useContext, useReducer, useEffect } from 'react'
import type { ReactNode } from 'react'
import { loginUser, logoutUser, apiFetch } from '@/lib/api'

// Types
interface User {
  id: string
  email: string
  name: string
  phone_number?: string
  city?: string
  country?: string
  social_media?: { platform: string; username_or_url: string }[]
  tujuan_pembayaran?: { bank_name: string; account_number: string; account_holder: string }[]
}

interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
}

interface LoginCredentials {
  email?: string
  phone_number?: string
  password: string
}

interface AuthContextValue extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
  clearError: () => void
}

// Action types
type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_ERROR'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' }

// Initial state
const initialState: AuthState = {
  user: null,
  isLoading: false,
  isAuthenticated: false,
  error: null,
}

// Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      }
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isLoading: false,
        user: action.payload,
        isAuthenticated: true,
        error: null,
      }
    case 'LOGIN_ERROR':
      return {
        ...state,
        isLoading: false,
        error: action.payload,
        user: null,
        isAuthenticated: false,
      }
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        error: null,
      }
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      }
    default:
      return state
  }
}

// Context
const AuthContext = createContext<AuthContextValue | undefined>(undefined)

// Provider
interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Ganti login mock dengan API backend
  const login = async (credentials: LoginCredentials): Promise<void> => {
    dispatch({ type: 'LOGIN_START' })
    try {
      // Panggil API login
      const res = await loginUser(credentials)
      // Simpan token
      localStorage.setItem('token', res.token)
      // Fetch profile user
      const profile = await apiFetch('/user', {
        method: 'GET',
      })
      const user: User = {
        id: profile.data.id,
        email: profile.data.email,
        name: profile.data.name,
        phone_number: profile.data.phone_number,
        city: profile.data.city,
        country: profile.data.country,
        social_media: profile.data.social_media,
        tujuan_pembayaran: profile.data.tujuan_pembayaran,
      }
      dispatch({ type: 'LOGIN_SUCCESS', payload: user })
    } catch (error) {
      dispatch({
        type: 'LOGIN_ERROR',
        payload: error instanceof Error ? error.message : 'Login gagal',
      })
    }
  }

  // Ganti logout agar revoke token ke backend dan hapus token
  const logout = async () => {
    try {
      await logoutUser()
    } catch {}
    localStorage.removeItem('token')
    dispatch({ type: 'LOGOUT' })
  }

  const refreshUser = async () => {
    try {
      const profile = await apiFetch('/user', { method: 'GET' })
      const user: User = {
        id: profile.data.id,
        email: profile.data.email,
        name: profile.data.name,
        phone_number: profile.data.phone_number,
        city: profile.data.city,
        country: profile.data.country,
        social_media: profile.data.social_media,
        tujuan_pembayaran: profile.data.tujuan_pembayaran,
      }
      dispatch({ type: 'LOGIN_SUCCESS', payload: user })
    } catch (error) {
      console.error('Failed to refresh user data:', error)
    }
  }

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' })
  }

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (token && !state.isAuthenticated && !state.isLoading) {
      (async () => {
        dispatch({ type: 'LOGIN_START' })
        try {
          const profile = await apiFetch('/user', { method: 'GET' })
          const user: User = {
            id: profile.data.id,
            email: profile.data.email,
            name: profile.data.name,
            phone_number: profile.data.phone_number,
            city: profile.data.city,
            country: profile.data.country,
            social_media: profile.data.social_media,
            tujuan_pembayaran: profile.data.tujuan_pembayaran,
          }
          dispatch({ type: 'LOGIN_SUCCESS', payload: user })
        } catch {
          localStorage.removeItem('token')
          dispatch({ type: 'LOGOUT' })
        }
      })()
    }
  }, [])

  const value: AuthContextValue = {
    ...state,
    login,
    logout,
    refreshUser,
    clearError,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook - moved to top level for Fast Refresh compatibility
export function useAuth(): AuthContextValue {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth harus digunakan dalam AuthProvider')
  }
  return context
}