{"version": 3, "names": ["_importDeferProxy", "init", "ns", "constV<PERSON>ue", "v", "proxy", "run", "_target", "p", "receiver", "Proxy", "defineProperty", "deleteProperty", "get", "Reflect", "getOwnPropertyDescriptor", "getPrototypeOf", "isExtensible", "has", "ownKeys", "preventExtensions", "set", "setPrototypeOf"], "sources": ["../../src/helpers/importDeferProxy.ts"], "sourcesContent": ["/* @minVersion 7.23.0 */\n\nexport default function _importDeferProxy<T extends object>(\n  init: () => T,\n): ProxyHandler<T> {\n  var ns: T | null = null;\n\n  var constValue = function <V extends boolean | null>(v: V) {\n    return function (): V {\n      return v;\n    };\n  };\n\n  var proxy = function (run: Function) {\n    return function (_target: T, p?: string | symbol, receiver?: any) {\n      if (ns === null) ns = init();\n      return run(ns, p, receiver);\n    };\n  };\n\n  return new Proxy(\n    {},\n    {\n      defineProperty: constValue(false),\n      deleteProperty: constValue(false),\n      get: proxy(Reflect.get),\n      getOwnPropertyDescriptor: proxy(Reflect.getOwnPropertyDescriptor),\n      getPrototypeOf: constValue(null),\n      isExtensible: constValue(false),\n      has: proxy(Reflect.has),\n      ownKeys: proxy(Reflect.ownKeys),\n      preventExtensions: constValue(true),\n      set: constValue(false),\n      setPrototypeOf: constValue(false),\n    },\n  );\n}\n"], "mappings": ";;;;;;AAEe,SAASA,iBAAiBA,CACvCC,IAAa,EACI;EACjB,IAAIC,EAAY,GAAG,IAAI;EAEvB,IAAIC,UAAU,GAAG,SAAAA,CAAoCC,CAAI,EAAE;IACzD,OAAO,YAAe;MACpB,OAAOA,CAAC;IACV,CAAC;EACH,CAAC;EAED,IAAIC,KAAK,GAAG,SAAAA,CAAUC,GAAa,EAAE;IACnC,OAAO,UAAUC,OAAU,EAAEC,CAAmB,EAAEC,QAAc,EAAE;MAChE,IAAIP,EAAE,KAAK,IAAI,EAAEA,EAAE,GAAGD,IAAI,CAAC,CAAC;MAC5B,OAAOK,GAAG,CAACJ,EAAE,EAAEM,CAAC,EAAEC,QAAQ,CAAC;IAC7B,CAAC;EACH,CAAC;EAED,OAAO,IAAIC,KAAK,CACd,CAAC,CAAC,EACF;IACEC,cAAc,EAAER,UAAU,CAAC,KAAK,CAAC;IACjCS,cAAc,EAAET,UAAU,CAAC,KAAK,CAAC;IACjCU,GAAG,EAAER,KAAK,CAACS,OAAO,CAACD,GAAG,CAAC;IACvBE,wBAAwB,EAAEV,KAAK,CAACS,OAAO,CAACC,wBAAwB,CAAC;IACjEC,cAAc,EAAEb,UAAU,CAAC,IAAI,CAAC;IAChCc,YAAY,EAAEd,UAAU,CAAC,KAAK,CAAC;IAC/Be,GAAG,EAAEb,KAAK,CAACS,OAAO,CAACI,GAAG,CAAC;IACvBC,OAAO,EAAEd,KAAK,CAACS,OAAO,CAACK,OAAO,CAAC;IAC/BC,iBAAiB,EAAEjB,UAAU,CAAC,IAAI,CAAC;IACnCkB,GAAG,EAAElB,UAAU,CAAC,KAAK,CAAC;IACtBmB,cAAc,EAAEnB,UAAU,CAAC,KAAK;EAClC,CACF,CAAC;AACH", "ignoreList": []}